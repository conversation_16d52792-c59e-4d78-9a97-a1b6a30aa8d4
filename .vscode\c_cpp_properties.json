{"configurations": [{"name": "Win32-Debug", "includePath": ["${workspaceFolder}/include", "${workspaceFolder}/**", "${env:CUDA_PATH}/include", "${env:VccLibs}/ffmpeg/include", "${env:VccLibs}/onnxruntime/include", "${env:VccLibs}/TensorRT/include", "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/include", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/um", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/shared", "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "WIN32", "WIN64", "_CONSOLE"], "windowsSdkVersion": "10.0.26100.0", "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64", "compileCommands": "${workspaceFolder}/compile_commands.json", "browse": {"path": ["${workspaceFolder}", "${env:VccLibs}/ffmpeg/include", "${env:VccLibs}/onnxruntime/include", "${env:VccLibs}/TensorRT/include", "${env:CUDA_PATH}/include"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db"}}, {"name": "Win32-Release", "includePath": ["${workspaceFolder}/include", "${workspaceFolder}/**", "${env:CUDA_PATH}/include", "${env:VccLibs}/ffmpeg/include", "${env:VccLibs}/onnxruntime/include", "${env:VccLibs}/TensorRT/include", "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/include", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/um", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/shared", "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include"], "defines": ["NDEBUG", "UNICODE", "_UNICODE", "WIN32", "WIN64", "_CONSOLE"], "windowsSdkVersion": "10.0.26100.0", "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64", "compileCommands": "${workspaceFolder}/compile_commands.json", "browse": {"path": ["${workspaceFolder}", "${env:VccLibs}/ffmpeg/include", "${env:VccLibs}/onnxruntime/include", "${env:VccLibs}/TensorRT/include", "${env:CUDA_PATH}/include"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db"}}], "version": 4}