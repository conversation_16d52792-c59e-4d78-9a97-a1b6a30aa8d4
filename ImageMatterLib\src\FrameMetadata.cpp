#include "FrameMetadata.h"
#include <cstring>

// Simple binary serialization format:
// [frameIndex:4][timestamp:8][flags:4][encodedAlphaSize:8][encodedAlpha:variable][numHeadDetections:8][headDetections:variable][processingStats:variable]

std::vector<unsigned char> FrameMetadata::Serialize() const {
    std::vector<unsigned char> buffer;
    
    // Calculate total size needed
    size_t totalSize = sizeof(frameIndex) + sizeof(timestamp) + 
                      sizeof(bool) * 4 + // flags
                      sizeof(size_t) + encodedAlpha.size() + // encoded alpha
                      sizeof(size_t) + headDetections.size() * sizeof(Box) + // head detections
                      sizeof(processingStats); // processing stats
    
    buffer.reserve(totalSize);
    
    // Helper lambda to append data
    auto appendData = [&buffer](const void* data, size_t size) {
        const unsigned char* bytes = static_cast<const unsigned char*>(data);
        buffer.insert(buffer.end(), bytes, bytes + size);
    };
    
    // Serialize frame identification
    appendData(&frameIndex, sizeof(frameIndex));
    appendData(&timestamp, sizeof(timestamp));
    
    // Serialize processing flags
    appendData(&hasInitialAlpha, sizeof(hasInitialAlpha));
    appendData(&hasHeadDetection, sizeof(hasHeadDetection));
    appendData(&hasBodyProcessing, sizeof(hasBodyProcessing));
    appendData(&hasHeadProcessing, sizeof(hasHeadProcessing));
    
    // Serialize encoded alpha data
    size_t alphaSize = encodedAlpha.size();
    appendData(&alphaSize, sizeof(alphaSize));
    if (alphaSize > 0) {
        appendData(encodedAlpha.data(), alphaSize);
    }
    
    // Serialize head detections
    size_t numDetections = headDetections.size();
    appendData(&numDetections, sizeof(numDetections));
    if (numDetections > 0) {
        appendData(headDetections.data(), numDetections * sizeof(Box));
    }
    
    // Serialize processing stats
    appendData(&processingStats, sizeof(processingStats));
    
    return buffer;
}

bool FrameMetadata::Deserialize(const std::vector<unsigned char>& data) {
    return Deserialize(data.data(), data.size(), *this);
}

bool FrameMetadata::Deserialize(const unsigned char* data, size_t size, FrameMetadata& metadata) {
    if (!data || size == 0) {
        return false;
    }
    
    const unsigned char* ptr = data;
    const unsigned char* end = data + size;
    
    // Helper lambda to read data safely
    auto readData = [&ptr, end](void* dest, size_t readSize) -> bool {
        if (ptr + readSize > end) {
            return false;
        }
        memcpy(dest, ptr, readSize);
        ptr += readSize;
        return true;
    };
    
    // Reset metadata
    metadata.Reset();
    
    // Deserialize frame identification
    if (!readData(&metadata.frameIndex, sizeof(metadata.frameIndex)) ||
        !readData(&metadata.timestamp, sizeof(metadata.timestamp))) {
        return false;
    }
    
    // Deserialize processing flags
    if (!readData(&metadata.hasInitialAlpha, sizeof(metadata.hasInitialAlpha)) ||
        !readData(&metadata.hasHeadDetection, sizeof(metadata.hasHeadDetection)) ||
        !readData(&metadata.hasBodyProcessing, sizeof(metadata.hasBodyProcessing)) ||
        !readData(&metadata.hasHeadProcessing, sizeof(metadata.hasHeadProcessing))) {
        return false;
    }
    
    // Deserialize encoded alpha data
    size_t alphaSize;
    if (!readData(&alphaSize, sizeof(alphaSize))) {
        return false;
    }
    
    if (alphaSize > 0) {
        if (ptr + alphaSize > end) {
            return false;
        }
        metadata.encodedAlpha.assign(ptr, ptr + alphaSize);
        ptr += alphaSize;
    }
    
    // Deserialize head detections
    size_t numDetections;
    if (!readData(&numDetections, sizeof(numDetections))) {
        return false;
    }
    
    if (numDetections > 0) {
        size_t detectionsSize = numDetections * sizeof(Box);
        if (ptr + detectionsSize > end) {
            return false;
        }
        metadata.headDetections.resize(numDetections);
        memcpy(metadata.headDetections.data(), ptr, detectionsSize);
        ptr += detectionsSize;
    }
    
    // Deserialize processing stats
    if (!readData(&metadata.processingStats, sizeof(metadata.processingStats))) {
        return false;
    }
    
    return true;
}
