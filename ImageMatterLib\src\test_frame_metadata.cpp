#include "FrameMetadata.h"
#include "FrameMetadataStorage.h"
#include <iostream>
#include <vector>
#include <cassert>

// Simple test to verify the new FrameMetadata and FrameMetadataStorage implementation
int Test() {
    std::cout << "Testing new FrameMetadata and FrameMetadataStorage implementation..." << std::endl;
    
    // Test 1: Basic FrameMetadata functionality
    std::cout << "\n=== Test 1: Basic FrameMetadata functionality ===" << std::endl;
    
    FrameMetadata metadata;
    metadata.frameIndex = 42;
    metadata.timestamp = 1.5;
    
    // Test encoded alpha data
    std::vector<unsigned char> alphaData = {100, 150, 200, 255, 0, 50};
    metadata.SetEncodedAlpha(alphaData);
    
    assert(metadata.hasInitialAlpha == true);
    assert(metadata.GetEncodedAlphaSize() == 6);
    assert(metadata.GetEncodedAlpha() == alphaData);
    
    // Test head detection data
    std::vector<Box> heads = {
        {100.0f, 200.0f, 50.0f, 60.0f, 0.9f},
        {300.0f, 400.0f, 45.0f, 55.0f, 0.8f}
    };
    metadata.SetHeadDetections(heads);
    
    assert(metadata.hasHeadDetection == true);
    assert(metadata.GetNumHeadDetections() == 2);
    assert(metadata.GetHeadDetections().size() == 2);
    
    std::cout << "✓ Basic FrameMetadata functionality works" << std::endl;
    
    // Test 2: Serialization/Deserialization
    std::cout << "\n=== Test 2: Serialization/Deserialization ===" << std::endl;
    
    std::vector<unsigned char> serialized = metadata.Serialize();
    assert(serialized.size() > 0);
    
    FrameMetadata deserialized;
    bool deserializeResult = FrameMetadata::Deserialize(serialized.data(), serialized.size(), deserialized);
    assert(deserializeResult == true);
    
    assert(deserialized.frameIndex == 42);
    assert(deserialized.timestamp == 1.5);
    assert(deserialized.hasInitialAlpha == true);
    assert(deserialized.hasHeadDetection == true);
    assert(deserialized.GetEncodedAlphaSize() == 6);
    assert(deserialized.GetNumHeadDetections() == 2);
    
    std::cout << "✓ Serialization/Deserialization works" << std::endl;
    
    // Test 3: RocksDB Storage
    std::cout << "\n=== Test 3: RocksDB Storage ===" << std::endl;
    
    std::wstring testDbPath = L"test_frame_metadata_db";
    
    // Create storage
    auto storage = FrameMetadataStorage::Create(testDbPath, true);
    assert(storage != nullptr);
    assert(storage->IsInitialized() == true);
    
    // Store metadata
    bool storeResult = storage->SetFrameMetadata(42, metadata);
    assert(storeResult == true);
    
    // Retrieve metadata
    FrameMetadata retrieved;
    bool retrieveResult = storage->GetFrameMetadata(42, retrieved);
    assert(retrieveResult == true);
    
    // Verify retrieved data
    assert(retrieved.frameIndex == 42);
    assert(retrieved.timestamp == 1.5);
    assert(retrieved.hasInitialAlpha == true);
    assert(retrieved.hasHeadDetection == true);
    assert(retrieved.GetEncodedAlphaSize() == 6);
    assert(retrieved.GetNumHeadDetections() == 2);
    
    // Test non-existent frame
    FrameMetadata nonExistent;
    bool nonExistentResult = storage->GetFrameMetadata(999, nonExistent);
    assert(nonExistentResult == false);
    
    // Test HasFrameMetadata
    assert(storage->HasFrameMetadata(42) == true);
    assert(storage->HasFrameMetadata(999) == false);
    
    // Test GetAllFrameIndices
    auto indices = storage->GetAllFrameIndices();
    assert(indices.size() == 1);
    assert(indices[0] == 42);
    
    // Test DeleteFrameMetadata
    bool deleteResult = storage->DeleteFrameMetadata(42);
    assert(deleteResult == true);
    assert(storage->HasFrameMetadata(42) == false);
    
    std::cout << "✓ RocksDB Storage works" << std::endl;
    
    // Test 4: Variable-length data with different sizes
    std::cout << "\n=== Test 4: Variable-length data with different sizes ===" << std::endl;
    
    // Test with large alpha data
    std::vector<unsigned char> largeAlphaData(1920 * 1080, 128); // Full HD frame worth of data
    FrameMetadata largeMetadata;
    largeMetadata.frameIndex = 100;
    largeMetadata.timestamp = 2.0;
    largeMetadata.SetEncodedAlpha(largeAlphaData);
    
    // Test with many head detections
    std::vector<Box> manyHeads;
    for (int i = 0; i < 50; ++i) {
        manyHeads.push_back({float(i * 10), float(i * 15), 30.0f, 35.0f, 0.7f});
    }
    largeMetadata.SetHeadDetections(manyHeads);
    
    // Store and retrieve large data
    bool storeLargeResult = storage->SetFrameMetadata(100, largeMetadata);
    assert(storeLargeResult == true);
    
    FrameMetadata retrievedLarge;
    bool retrieveLargeResult = storage->GetFrameMetadata(100, retrievedLarge);
    assert(retrieveLargeResult == true);
    
    assert(retrievedLarge.GetEncodedAlphaSize() == 1920 * 1080);
    assert(retrievedLarge.GetNumHeadDetections() == 50);
    
    std::cout << "✓ Variable-length data with different sizes works" << std::endl;
    
    // Cleanup
    storage.reset();
    
    std::cout << "\n=== All tests passed! ===" << std::endl;
    std::cout << "The new FrameMetadata and FrameMetadataStorage implementation is working correctly." << std::endl;
    
    return 0;
}
