# Migration vers TensorRT 10.x

## Changements d'API principaux

### 1. Bindings → Tensor Addresses

**Avant (TensorRT 8.x/9.x):**
```cpp
// Ancienne API avec bindings
int numBindings = engine->getNbBindings();
void** bindings = new void*[numBindings];
bindings[inputIndex] = inputBuffer;
bindings[outputIndex] = outputBuffer;
context->enqueueV2(bindings, stream, nullptr);
```

**Après (TensorRT 10.x):**
```cpp
// Nouvelle API avec tensor addresses
int numIOTensors = engine->getNbIOTensors();
const char* inputName = engine->getIOTensorName(0);
const char* outputName = engine->getIOTensorName(1);
context->setTensorAddress(inputName, inputBuffer);
context->setTensorAddress(outputName, outputBuffer);
context->enqueueV3(stream);
```

### 2. Méthodes d'inspection des tensors

**Avant:**
```cpp
engine->getBindingName(i)
engine->bindingIsInput(i)
engine->getBindingDimensions(i)
```

**Après:**
```cpp
engine->getIOTensorName(i)
engine->getTensorIOMode(name)  // Returns TensorIOMode::kINPUT or kOUTPUT
engine->getTensorShape(name)
```

### 3. Enqueue methods

**Avant:**
```cpp
context->enqueueV2(bindings, stream, nullptr)
```

**Après:**
```cpp
context->enqueueV3(stream)
```

## Code corrigé pour ImageMattingTensorRt

### SetupBindings() - TensorRT 10.x

```cpp
bool ImageMattingTensorRt::SetupBindings() {
    try {
        // TensorRT 10.x API
        int numIOTensors = m_engine->getNbIOTensors();
        
        for (int i = 0; i < numIOTensors; ++i) {
            const char* name = m_engine->getIOTensorName(i);
            nvinfer1::TensorIOMode ioMode = m_engine->getTensorIOMode(name);
            auto dims = m_engine->getTensorShape(name);

            if (ioMode == nvinfer1::TensorIOMode::kINPUT) {
                m_inputIndex = i;
                m_inputName = name;
                m_modelInputWidth = dims.d[3];  // Assuming NCHW format
                m_modelInputHeight = dims.d[2];
                
                // Set input tensor address
                m_context->setTensorAddress(name, m_deviceResizedInputBuffer);
            } else if (ioMode == nvinfer1::TensorIOMode::kOUTPUT) {
                m_outputIndex = i;
                m_outputName = name;
                
                // Set output tensor address
                m_context->setTensorAddress(name, m_deviceResizedOutputBuffer);
            }
        }

        return (m_inputIndex >= 0 && m_outputIndex >= 0);
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in SetupBindings: " << e.what() << std::endl;
        return false;
    }
}
```

### Infer() - TensorRT 10.x

```cpp
bool ImageMattingTensorRt::Infer() {
    if (!m_initialized || !m_context) {
        std::cerr << "ImageMattingTensorRt not initialized" << std::endl;
        return false;
    }

    try {
        // Preprocess input (same as ImageMatting)
        cudaError_t preprocessResult = PreprocessInputBuffer(m_deviceInputBuffer, m_deviceResizedInputBuffer);
        if (preprocessResult != cudaSuccess) {
            std::cerr << "Preprocessing failed: " << cudaGetErrorString(preprocessResult) << std::endl;
            return false;
        }

        // Run TensorRT inference (TensorRT 10.x API)
        bool inferenceResult = m_context->enqueueV3(m_cudaStream);
        if (!inferenceResult) {
            std::cerr << "TensorRT inference failed" << std::endl;
            return false;
        }

        // Postprocess output (resize back to original dimensions)
        cudaError_t postprocessResult = PostprocessOutputMask(
            m_deviceResizedOutputBuffer, m_deviceOutputBuffer,
            m_modelInputWidth, m_modelInputHeight,
            m_imageWidth, m_imageHeight,
            m_cudaStream);

        if (postprocessResult != cudaSuccess) {
            std::cerr << "Output postprocessing failed: " << cudaGetErrorString(postprocessResult) << std::endl;
            return false;
        }

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in ImageMattingTensorRt::Infer: " << e.what() << std::endl;
        return false;
    }
}
```

## Preprocessing unifié avec ImageMatting

Le code de preprocessing est maintenant identique à ImageMatting :

```cpp
cudaError_t ImageMattingTensorRt::PreprocessInputBuffer(const float* inputBuffer, float* outputBuffer) {
    if (!m_initialized || !inputBuffer || !outputBuffer) {
        return cudaErrorInvalidValue;
    }
    bool needsResize = (m_imageWidth != m_modelInputWidth || m_imageHeight != m_modelInputHeight);

    cudaError_t cudaStatus = cudaSuccess;

    // Preprocess the input buffer using the launcher function (same as ImageMatting)
    cudaStatus = LaunchPreprocessBufferKernel(m_preprocessedBuffer, inputBuffer, m_imageWidth, m_imageHeight, m_isRgba, m_normalizationParams, m_cudaStream);
    if (cudaStatus != cudaSuccess) {
        return cudaStatus;
    }
    cudaStatus = cudaStreamSynchronize(m_cudaStream);
    if (cudaStatus != cudaSuccess) {
        std::cerr << "Failed to synchronize after preprocessing: " << cudaGetErrorString(cudaStatus) << std::endl;
        return cudaStatus;
    }

    // Resize if needed
    if (needsResize) {
        cudaStatus = LanczosResizeKernelLauncher(outputBuffer, m_modelInputWidth, m_modelInputHeight,
            m_preprocessedBuffer, m_imageWidth, m_imageHeight,
            m_isRgba ? 4 : 3, m_cudaStream);
        if (cudaStatus != cudaSuccess) {
            return cudaStatus;
        }
        cudaStatus = cudaStreamSynchronize(m_cudaStream);
        if (cudaStatus != cudaSuccess) {
            std::cerr << "Failed to synchronize after resizing: " << cudaGetErrorString(cudaStatus) << std::endl;
            return cudaStatus;
        }
    }
    else {
        // If no resize needed, just copy the data
        cudaStatus = cudaMemcpyAsync(outputBuffer, m_preprocessedBuffer, m_preprocessedBufferSize,
            cudaMemcpyDeviceToDevice, m_cudaStream);
        if (cudaStatus != cudaSuccess) {
            return cudaStatus;
        }
    }

    return cudaSuccess;
}
```

## Changements dans le header

Suppression des références aux bindings :

```cpp
// Supprimé :
void** m_bindings;  // TensorRT bindings array

// Les tensor addresses sont maintenant gérées directement dans SetupBindings()
```

## Compilation

Assurez-vous d'avoir TensorRT 10.x installé et lié correctement :

```cmake
find_package(TensorRT REQUIRED)
target_link_libraries(${PROJECT_NAME} PRIVATE ${TensorRT_LIBRARIES})
```

## Avantages de TensorRT 10.x

1. **API plus simple** : Plus besoin de gérer manuellement les bindings
2. **Performance améliorée** : Optimisations supplémentaires
3. **Meilleure gestion mémoire** : Allocation plus efficace
4. **Support étendu** : Nouvelles architectures GPU
