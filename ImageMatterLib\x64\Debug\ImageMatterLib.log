﻿  DetectHeadsStep.cpp
  FindInitialAlphaStep.cpp
  FrameMetadata.cpp
  FrameMetadataStorage.cpp
F:\VccLibs\rocksdb\include\rocksdb\advanced_options.h(24,8): warning C4099: 'rocksdb::Options' : nom de type déjà rencontré avec 'class' et utilisant maintenant 'struct'
  (compiler le fichier source 'src/FrameMetadataStorage.cpp')
      F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\include_internal\FrameMetadataStorage.h(10,11):
      voir la déclaration de 'rocksdb::Options'
  
F:\VccLibs\rocksdb\include\rocksdb\options.h(61,8): warning C4099: 'rocksdb::Options' : nom de type déjà rencontré avec 'class' et utilisant maintenant 'struct'
  (compiler le fichier source 'src/FrameMetadataStorage.cpp')
      F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\include_internal\FrameMetadataStorage.h(10,11):
      voir la déclaration de 'rocksdb::Options'
  
F:\VccLibs\rocksdb\include\rocksdb\options.h(1653,8): warning C4099: 'rocksdb::Options' : nom de type déjà rencontré avec 'class' et utilisant maintenant 'struct'
  (compiler le fichier source 'src/FrameMetadataStorage.cpp')
      F:\VccLibs\rocksdb\include\rocksdb\options.h(1653,8):
      voir la déclaration de 'rocksdb::Options'
  
  ProcessBodyRegionsStep.cpp
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\src\ProcessBodyRegionsStep.cpp(173,57): error C2039: 'encodedAlphaSize' n'est pas membre de 'FrameMetadata'
      F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\include_internal\FrameMetadata.h(14,8):
      voir la déclaration de 'FrameMetadata'
  
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\src\ProcessBodyRegionsStep.cpp(180,23): error C2039: 'encodedAlphaSize' n'est pas membre de 'FrameMetadata'
      F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\include_internal\FrameMetadata.h(14,8):
      voir la déclaration de 'FrameMetadata'
  
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\src\ProcessBodyRegionsStep.cpp(182,48): error C2039: 'encodedAlphaSize' n'est pas membre de 'FrameMetadata'
      F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\include_internal\FrameMetadata.h(14,8):
      voir la déclaration de 'FrameMetadata'
  
  ProcessHeadRegionsStep.cpp
  ProcessingStep.cpp
  VideoBackgroundRemover.cpp
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\src\VideoBackgroundRemover.cpp(331,72): error C2100: vous ne pouvez pas déréférencer un opérande de type 'FrameMetadata'
  test_frame_metadata.cpp
  Génération de code en cours...
