#pragma once

#include "ProcessingStep.h"
#include "FrameMetadata.h"
#include "ImageMatting.h"
#include <memory>

/**
 * Processing step for generating initial alpha matte
 * Uses InsPyReNet or similar model to generate initial alpha matte and encode it
 */
class FindInitialAlphaStep : public ProcessingStep {
public:
    FindInitialAlphaStep();
    virtual ~FindInitialAlphaStep();

    // ProcessingStep interface implementation
    bool Initialize(int width, int height, CUcontext cudaContext, cudaStream_t stream) override;
    bool Process(float* rgbBuffer, FrameMetadata& frameMetadata) override;
    const char* GetStepName() const override { return "FindInitialAlpha"; }
    bool IsInitialized() const override { return m_initialized; }

private:
    // AI model for initial alpha matting
    std::unique_ptr<ImageMatting> m_initialImageMatter;

    // Processing parameters
    int m_width;
    int m_height;
    CUcontext m_cudaContext;
    cudaStream_t m_stream;
    bool m_initialized;

    // Compression buffer for alpha encoding
    unsigned char* m_compressionBuffer;
    size_t m_compressionBufferSize;

    // Helper methods
    bool AllocateBuffers();
    void DeallocateBuffers();
    bool EncodeAlpha(float* alphaBuffer, unsigned char** encodedData, size_t* encodedSize);
};
