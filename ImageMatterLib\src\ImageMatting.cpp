#define NOMINMAX // Disable the min/max macros from windows.h which interfere with std::min/max

#include "ImageMatting.h"
#include "ImageMattingFactory.h"  // For ResizeMethod enum
#include "Matting_Kernels.cuh"
#include "DownscaleResizingKernels.cuh"
#include "UpscaleResizingKernels.cuh"
#include "HeadDetectorKernels.cuh"  // For LanczosResizeAndPadKernel
#include <iostream>

// Common preprocessing implementation (non-static method)
cudaError_t ImageMatting::PreprocessInputBufferCommon(
    const float* inputBuffer,
    float* preprocessedBuffer,
    float* outputBuffer,
    int imageWidth,
    int imageHeight,
    int modelWidth,
    int modelHeight,
    bool isRgba,
    const NormalizationParams& normParams,
    size_t preprocessedBufferSize,
    ResizeMethod resizeMethod,
    cudaStream_t stream) {

    if (!inputBuffer || !outputBuffer) {
        return cudaErrorInvalidValue;
    }

    bool needsResize = (imageWidth != modelWidth || imageHeight != modelHeight);
    cudaError_t cudaStatus = cudaSuccess;

    // Step 1: Preprocess the input buffer (normalization)
    if (needsResize) {
        // When resizing is needed, preprocess into intermediate buffer first
        if (!preprocessedBuffer) {
            return cudaErrorInvalidValue;
        }
        cudaStatus = LaunchPreprocessBufferKernel(preprocessedBuffer, inputBuffer,
                                                imageWidth, imageHeight, isRgba,
                                                normParams, stream);
    } else {
        // When no resizing is needed, preprocess directly into output buffer
        cudaStatus = LaunchPreprocessBufferKernel(outputBuffer, inputBuffer,
                                                imageWidth, imageHeight, isRgba,
                                                normParams, stream);
    }
    if (cudaStatus != cudaSuccess) {
        std::cerr << "Failed to preprocess buffer: " << cudaGetErrorString(cudaStatus) << std::endl;
        return cudaStatus;
    }

    // Step 2: Resize if needed
    if (needsResize) {
        if (resizeMethod == ResizeMethod::EXTEND_SHRINK_LANCZOS) {
            // Use direct Lanczos resize (extend/shrink as needed)
            cudaStatus = LanczosResizeKernelLauncher(outputBuffer, modelWidth, modelHeight,
                preprocessedBuffer, imageWidth, imageHeight,
                isRgba ? 4 : 3, stream);
        }
        else if (resizeMethod == ResizeMethod::STRETCH_ASPECT_RATIO_PAD) {
            // Use Lanczos resize with aspect ratio preservation and padding
            // Note: The HeadDetectorKernels version only supports RGB (3 channels)
            if (isRgba) {
                std::cerr << "STRETCH_ASPECT_RATIO_PAD resize method does not support RGBA input yet" << std::endl;
                return cudaErrorNotSupported;
            }
            cudaStatus = LaunchLanczosResizeAndPadKernel(outputBuffer, modelWidth, modelHeight,
                const_cast<float*>(preprocessedBuffer), imageWidth, imageHeight);
        }
        else {
            std::cerr << "Unknown resize method" << std::endl;
            return cudaErrorInvalidValue;
        }

        if (cudaStatus != cudaSuccess) {
            std::cerr << "Failed to resize input: " << cudaGetErrorString(cudaStatus) << std::endl;
            return cudaStatus;
        }
    }
    else {
        // If no resize needed, preprocessing was already done directly into output buffer
        // No additional copy needed
    }

    return cudaSuccess;
}

// Common postprocessing implementation (non-static method)
cudaError_t ImageMatting::PostprocessOutputBufferCommon(
    const float* modelOutput,
    float* outputBuffer,
    int modelWidth,
    int modelHeight,
    int imageWidth,
    int imageHeight,
    ResizeMethod resizeMethod,
    cudaStream_t stream) {

    // If dimensions are the same, just copy the data
    if (modelWidth == imageWidth && modelHeight == imageHeight) {
        return cudaMemcpyAsync(outputBuffer, modelOutput,
            imageWidth * imageHeight * sizeof(float),
            cudaMemcpyDeviceToDevice, stream);
    }

    if (resizeMethod == ResizeMethod::EXTEND_SHRINK_LANCZOS) {
        // Use direct Lanczos upscaling for better quality
        LanczosUpscaleKernelLauncher(outputBuffer, imageWidth, imageHeight,
                                    modelOutput, modelWidth, modelHeight, 1, stream);
    }
    else if (resizeMethod == ResizeMethod::STRETCH_ASPECT_RATIO_PAD) {
        // For aspect ratio preserved input, we need to "uncrop" the padded output back to original size
        // During preprocessing, the image was resized with aspect ratio preservation and padded
        // Now we need to extract the content region from the padded model output and resize back
        float scale = std::min(static_cast<float>(modelWidth) / imageWidth,
                              static_cast<float>(modelHeight) / imageHeight);
        int scaledWidth = static_cast<int>(imageWidth * scale);
        int scaledHeight = static_cast<int>(imageHeight * scale);
        int padLeft = (modelWidth - scaledWidth) / 2;
        int padTop = (modelHeight - scaledHeight) / 2;

        // Step 1: Extract the content region from the padded model output
        // The model output contains padding, we need to extract the center region that contains actual content
        // This region is [padLeft, padTop, scaledWidth, scaledHeight] within the model output

        // For now, allocate a temporary buffer for the cropped region
        float* croppedBuffer = nullptr;
        size_t croppedBufferSize = scaledWidth * scaledHeight * sizeof(float);
        cudaError_t allocResult = cudaMalloc(&croppedBuffer, croppedBufferSize);
        if (allocResult != cudaSuccess) {
            std::cerr << "Failed to allocate temporary buffer for cropping: " << cudaGetErrorString(allocResult) << std::endl;
            return allocResult;
        }

        // Step 2: Extract the cropped region using cudaMemcpy2D
        // Copy the relevant region from modelOutput to croppedBuffer
        cudaError_t copyResult = cudaMemcpy2DAsync(
            croppedBuffer,                                 // dst
            scaledWidth * sizeof(float),                   // dst pitch
            modelOutput + padTop * modelWidth + padLeft,   // src (offset to start of content)
            modelWidth * sizeof(float),                    // src pitch
            scaledWidth * sizeof(float),                   // width in bytes
            scaledHeight,                                  // height
            cudaMemcpyDeviceToDevice,
            stream
        );
        if (copyResult != cudaSuccess) {
            std::cerr << "Failed to copy cropped region: " << cudaGetErrorString(copyResult) << std::endl;
            cudaFree(croppedBuffer);
            return copyResult;
        }

        // Step 3: Resize the cropped region to original image size
        LanczosUpscaleKernelLauncher(outputBuffer, imageWidth, imageHeight,
                                    croppedBuffer, scaledWidth, scaledHeight, 1, stream);

        // Clean up temporary buffer
        cudaFree(croppedBuffer);
    }
    else {
        std::cerr << "Unknown resize method in postprocessing" << std::endl;
        return cudaErrorInvalidValue;
    }

    return cudaGetLastError();
}
