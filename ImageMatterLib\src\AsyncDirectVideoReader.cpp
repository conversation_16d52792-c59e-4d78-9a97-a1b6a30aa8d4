#include "AsyncDirectVideoReader.h"
#include <iostream>
#include <chrono>
#include <algorithm>

// Constructor
AsyncDirectVideoReader::AsyncDirectVideoReader()
	: m_cudaContext(nullptr), m_maxBufferSize(3), m_initialized(false), m_frameBufferSize(0) {
}

// Destructor
AsyncDirectVideoReader::~AsyncDirectVideoReader() {
	StopAsync();
}

// Create factory method
std::shared_ptr<AsyncDirectVideoReader> AsyncDirectVideoReader::Create(
	const std::string& filePath,
	CUcontext cudaContext,
	bool enableHardwareAcceleration,
	int bufferSize) {

	auto reader = std::shared_ptr<AsyncDirectVideoReader>(new AsyncDirectVideoReader());

	if (!reader->Initialize(filePath, cudaContext, enableHardwareAcceleration, bufferSize)) {
		return nullptr;
	}

	if (!reader->StartAsync()) {
		return nullptr;
	}

	return reader;
}

// Initialize the reader
bool AsyncDirectVideoReader::Initialize(const std::string& filePath, CUcontext cudaContext,
	bool enableHardwareAcceleration, int bufferSize) {
	if (bufferSize <= 0) {
		std::cerr << "Invalid buffer size: " << bufferSize << std::endl;
		return false;
	}

	m_cudaContext = cudaContext;
	m_maxBufferSize = bufferSize;

	// Create the underlying DirectVideoReader
	m_reader = DirectVideoReader::Create(filePath, cudaContext, enableHardwareAcceleration);
	if (!m_reader) {
		std::cerr << "Failed to create DirectVideoReader for: " << filePath << std::endl;
		return false;
	}

	// Calculate frame buffer properties for planar float RGB output
	int width = m_reader->GetWidth();
	int height = m_reader->GetHeight();

	// RGB float format: 3 channels (R, G, B) * sizeof(float) per pixel
	m_frameBufferSize = width * height * 3 * sizeof(float);

	m_initialized = true;
	return true;
}

// Start asynchronous reading
bool AsyncDirectVideoReader::StartAsync() {
	if (!m_initialized) {
		return false;
	}

	m_stopReading = false;
	m_bufferingError = false;

	try {
		m_readingThread = std::thread(&AsyncDirectVideoReader::ReadingThreadFunction, this);
		return true;
	}
	catch (const std::exception& e) {
		std::cerr << "Failed to start reading thread: " << e.what() << std::endl;
		return false;
	}
}

// Stop asynchronous reading
void AsyncDirectVideoReader::StopAsync() {
	if (m_readingThread.joinable()) {
		m_stopReading = true;
		m_readerCondition.notify_all();
		m_bufferCondition.notify_all();
		m_readingThread.join();
	}

	ClearBuffer();
}

// Background thread function for reading frames
void AsyncDirectVideoReader::ReadingThreadFunction() {
	// Set CUDA context for this thread
	CUresult contextResult = cuCtxSetCurrent(m_cudaContext);
	if (contextResult != CUDA_SUCCESS) {
		std::cerr << "ERROR: Failed to set CUDA context in reading thread: " << contextResult << std::endl;
		m_bufferingError = true;
		return;
	}

	std::cout << "AsyncDirectVideoReader: Reading thread started" << std::endl;

	while (!m_stopReading.load()) {
		// Check if seek was requested
		if (m_seekRequested.load()) {
			double seekTime = m_seekTime.load();

			// Clear current buffer
			{
				std::lock_guard<std::mutex> lock(m_bufferMutex);
				ClearBuffer();
			}

			// Perform seek
			if (!m_reader->Seek(seekTime)) {
				std::cerr << "Failed to seek to time: " << seekTime << std::endl;
				m_bufferingError = true;
			}

			m_seekRequested = false;
			m_bufferCondition.notify_all();
			continue;
		}

		// Check if buffer is full
		{
			std::unique_lock<std::mutex> lock(m_bufferMutex);
			if (m_frameBuffer.size() >= static_cast<size_t>(m_maxBufferSize)) {
				// Wait for space in buffer or stop signal
				m_readerCondition.wait(lock, [this] {
					return m_frameBuffer.size() < static_cast<size_t>(m_maxBufferSize) ||
						   m_stopReading.load() || m_seekRequested.load();
				});
				continue;
			}
		}

		// Read next frame
		auto bufferedFrame = std::make_unique<BufferedFrame>();

		// Allocate CUDA buffer for this frame (for RGB float data)
		cudaError_t cudaResult = cudaMalloc(reinterpret_cast<void**>(&bufferedFrame->cudaBuffer), m_frameBufferSize);
		if (cudaResult != cudaSuccess) {
			std::cerr << "Failed to allocate CUDA buffer for frame: " << cudaGetErrorString(cudaResult) << std::endl;
			m_bufferingError = true;
			break;
		}

		bufferedFrame->bufferSize = m_frameBufferSize;

		// Read frame from underlying reader directly into RGB float format
		// DirectVideoReader::ReadFrame now returns double (timestamp) and takes float* as output
		double timestamp = m_reader->ReadFrame(bufferedFrame->cudaBuffer,
			bufferedFrame->bufferSize); // Pass timestamp_out by reference

		if (timestamp < 0.0) { // Check the returned timestamp for error/EOF
			// End of stream or error
			bufferedFrame->isEndOfStream = true;
			bufferedFrame->isValid = false;
			bufferedFrame->timestamp = -1.0;
			// Free the allocated buffer if the read failed, as ownership isn't transferred
			if (bufferedFrame->cudaBuffer) {
				cudaFree(bufferedFrame->cudaBuffer);
				bufferedFrame->cudaBuffer = nullptr;
			}
		}
		else {
			bufferedFrame->isValid = true;
			bufferedFrame->timestamp = timestamp;
			// The buffer in bufferedFrame->cudaBuffer should already contain the data
			// as it was passed as the output buffer to m_reader->ReadFrame.
		}

		// Add frame to buffer
		{
			std::lock_guard<std::mutex> lock(m_bufferMutex);
			m_frameBuffer.push(std::move(bufferedFrame));
		}

		// Notify waiting consumers
		m_bufferCondition.notify_one();

		// If end of stream, stop reading
		if (timestamp < 0.0) {
			break;
		}
	}

	std::cout << "AsyncDirectVideoReader: Reading thread stopped" << std::endl;
}

// Read the next frame, returning the timestamp and the CUDA buffer directly via parameters
double AsyncDirectVideoReader::ReadFrame(float** cudaBuffer, size_t* bufferSize) {
	if (!m_initialized || !cudaBuffer || !bufferSize) {
		return -1.0; // Return -1.0 to indicate error
	}

	std::unique_ptr<BufferedFrame> frame;

	// Get next frame from buffer
	{
		std::unique_lock<std::mutex> lock(m_bufferMutex);

		// Wait for frame to be available
		m_bufferCondition.wait(lock, [this] {
			return !m_frameBuffer.empty() || m_bufferingError.load();
		});

		if (m_bufferingError.load()) {
			return -1.0; // Return -1.0 to indicate error
		}

		if (m_frameBuffer.empty()) {
			return -1.0; // Return -1.0 to indicate error
		}

		frame = std::move(m_frameBuffer.front());
		m_frameBuffer.pop();
	}

	// Notify reading thread that space is available
	m_readerCondition.notify_one();

	if (!frame) {
		return -1.0; // Return -1.0 to indicate error
	}

	if (frame->isEndOfStream) {
		return -1.0; // Return -1.0 to indicate end of stream
	}

	if (!frame->isValid) {
		return -1.0; // Return -1.0 to indicate invalid frame
	}

	// Return the frame buffer directly (caller takes ownership)
	*cudaBuffer = frame->cudaBuffer;
	*bufferSize = frame->bufferSize;

	// Release ownership from the unique_ptr so it doesn't get freed by BufferedFrame destructor
	frame->cudaBuffer = nullptr;

	return frame->timestamp; // Return the timestamp directly
}

// Seek to a specific time position
bool AsyncDirectVideoReader::Seek(double timeInSeconds) {
	if (!m_initialized) {
		return false;
	}

	m_seekTime = timeInSeconds;
	m_seekRequested = true;
	m_readerCondition.notify_all();

	// Wait for seek to complete
	std::unique_lock<std::mutex> lock(m_bufferMutex);
	m_bufferCondition.wait(lock, [this] {
		return !m_seekRequested.load() || m_bufferingError.load();
	});

	return !m_bufferingError.load();
}

// Clear the frame buffer
void AsyncDirectVideoReader::ClearBuffer() {
	while (!m_frameBuffer.empty()) {
		m_frameBuffer.pop();
	}
}

// Get the current buffer fill level
int AsyncDirectVideoReader::GetBufferFillLevel() const {
	std::lock_guard<std::mutex> lock(m_bufferMutex);
	return static_cast<int>(m_frameBuffer.size());
}
