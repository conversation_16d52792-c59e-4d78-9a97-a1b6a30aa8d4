﻿#include "HeadDetectorKernels.cuh"
#include <device_launch_parameters.h>
#include <math_constants.h>

#ifdef __CUDACC__

__device__ float lanczos_weight(float x, float a = 3.0f) {
    const float eps = 1e-6f;
    if (fabsf(x) < eps) return 1.0f;
    if (fabsf(x) >= a) return 0.0f;
    float pi_x = CUDART_PI_F * x;
    float pi_x_a = pi_x / a;
    return (sinf(pi_x) / pi_x) * (sinf(pi_x_a) / pi_x_a);
}
// NB: input is assumed to be a float interleaved RGB image.
// Input is now a planar RGB buffer: [RRR...][GGG...][BBB...]
__global__ void LanczosResizeAndPadKernel(
    float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    float padValue)
{
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    int c = blockIdx.z * blockDim.z + threadIdx.z;

    // Only process R, G, B (skip alpha)
    if (x >= outputWidth || y >= outputHeight || c >= 3)  // c ∈ {0,1,2} (R,G,B)
        return;

    // Compute scale factor (preserve aspect ratio)
    float scale = fminf(
        (float)outputWidth / (float)inputWidth,
        (float)outputHeight / (float)inputHeight
    );

    // Compute resized dimensions (before padding)
    int resizedWidth = (int)(scale * inputWidth + 0.5f);
    int resizedHeight = (int)(scale * inputHeight + 0.5f);

    // Compute padding to center the resized image
    int padLeft = (outputWidth - resizedWidth) / 2;
    int padTop = (outputHeight - resizedHeight) / 2;

    // Check if current pixel is in the padding region
    if (x < padLeft || x >= (padLeft + resizedWidth) ||
        y < padTop || y >= (padTop + resizedHeight)) {
        int outIdx = (c * outputHeight + y) * outputWidth + x;
        output[outIdx] = padValue;
        return;
    }

    // Map output coordinates to input coordinates
    float srcX = (float)(x - padLeft) * (float)(inputWidth - 1) / (float)(resizedWidth - 1);
    float srcY = (float)(y - padTop) * (float)(inputHeight - 1) / (float)(resizedHeight - 1);

    // Lanczos interpolation
    const float a = 3.0f;
    int x_start = max(0, (int)floor(srcX - a + 0.5f));
    int x_end = min(inputWidth - 1, (int)floor(srcX + a + 0.5f));
    int y_start = max(0, (int)floor(srcY - a + 0.5f));
    int y_end = min(inputHeight - 1, (int)floor(srcY + a + 0.5f));

    float sum = 0.0f;
    float weight_sum = 0.0f;

    for (int j = y_start; j <= y_end; ++j) {
        float dy = srcY - j;
        float wy = lanczos_weight(dy, a);
        if (wy == 0.0f) continue;

        for (int i = x_start; i <= x_end; ++i) {
            float dx = srcX - i;
            float wx = lanczos_weight(dx, a);
            if (wx == 0.0f) continue;

            float weight = wx * wy;
            // Input is planar RGB → channel c is at offset (c * planeSize)
            int planeSize = inputWidth * inputHeight;
            float pixel = input[c * planeSize + j * inputWidth + i]; // Planar access
            sum += pixel * weight;
            weight_sum += weight;
        }
    }

    int outIdx = (c * outputHeight + y) * outputWidth + x;
    output[outIdx] = (weight_sum > 0.0f) ? (sum / weight_sum) : padValue;
}
cudaError_t LaunchLanczosResizeAndPadKernel(
    float* output, int outputWidth, int outputHeight,
    float* input, int inputWidth, int inputHeight)
{
    dim3 block(16, 16, 1);
    dim3 grid(
        (outputWidth + block.x - 1) / block.x,
        (outputHeight + block.y - 1) / block.y,
        3  // Only R, G, B (no alpha)
    );

    LanczosResizeAndPadKernel << <grid, block >> > (
        output, outputWidth, outputHeight,
        input, inputWidth, inputHeight,
        0.0f  // padValue
        );

    return cudaGetLastError();
}

#endif 