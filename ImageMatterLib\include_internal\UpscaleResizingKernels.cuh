#ifndef UPSCALE_KERNELS_H
#define UPSCALE_KERNELS_H

#include <cuda_runtime.h>

#ifdef __cplusplus
extern "C" {
#endif

    // Bicubic upscaling kernel functions (smooth, natural results)
    cudaError_t LaunchBicubicUpscaleKernel(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels);

    cudaError_t BicubicUpscaleKernelLauncher(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels, cudaStream_t stream);

    // Edge-directed upscaling kernel functions (preserves edges, reduces artifacts)
    cudaError_t LaunchEdgeDirectedUpscaleKernel(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels);

    cudaError_t EdgeDirectedUpscaleKernelLauncher(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels, cudaStream_t stream);

    // Lanczos upscaling kernel functions (sharp, detailed results)
    cudaError_t LaunchLanczosUpscaleKernel(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels);

    cudaError_t LanczosUpscaleKernelLauncher(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels, cudaStream_t stream);

#ifdef __cplusplus
}
#endif

#endif // UPSCALE_KERNELS_H