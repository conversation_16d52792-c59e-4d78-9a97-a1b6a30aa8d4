#pragma once

#include <memory>
#include <string>
#include "FrameMetadata.h"

// Forward declaration for RocksDB
namespace rocksdb {
    class DB;
    class Options;
}

/**
 * RocksDB-based storage for frame metadata persistence between processing passes
 * Provides efficient storage and retrieval of frame metadata with variable-length data support
 */
class FrameMetadataStorage {
public:
    /**
     * Create or open a RocksDB database for frame metadata storage
     * @param dbPath Path to the RocksDB database directory
     * @param createNew If true, create a new database (overwriting existing), if false, open existing
     * @return Unique pointer to storage instance, nullptr on failure
     */
    static std::unique_ptr<FrameMetadataStorage> Create(
        const std::wstring& dbPath,
        bool createNew = true);

    /**
     * Destructor - automatically closes file and unmaps memory
     */
    ~FrameMetadataStorage();

    /**
     * Get frame metadata by index
     * @param frameIndex Index of the frame (0-based)
     * @param metadata Output parameter to store the retrieved metadata
     * @return True if found and retrieved successfully, false otherwise
     */
    bool GetFrameMetadata(int frameIndex, FrameMetadata& metadata) const;

    /**
     * Set frame metadata by index
     * @param frameIndex Frame index (0-based)
     * @param metadata Metadata to set
     * @return True on success, false on failure
     */
    bool SetFrameMetadata(int frameIndex, const FrameMetadata& metadata);

    /**
     * Delete frame metadata by index
     * @param frameIndex Frame index (0-based)
     * @return True if successful, false on error
     */
    bool DeleteFrameMetadata(int frameIndex);

    /**
     * Check if frame metadata exists for given index
     * @param frameIndex Frame index (0-based)
     * @return True if exists, false otherwise
     */
    bool HasFrameMetadata(int frameIndex) const;

    /**
     * Get all frame indices that have metadata stored
     * @return Vector of frame indices
     */
    std::vector<int> GetAllFrameIndices() const;

    /**
     * Flush any pending writes to disk
     * @return True on success, false on failure
     */
    bool Flush();

    /**
     * Check if the storage is properly initialized
     * @return True if initialized, false otherwise
     */
    bool IsInitialized() const { return m_initialized; }

    /**
     * Get the database path
     * @return Database path as wide string
     */
    const std::wstring& GetDbPath() const { return m_dbPath; }

private:
    // Private constructor - use Create() method
    FrameMetadataStorage();

    // Initialize the storage
    bool Initialize(const std::wstring& dbPath, bool createNew);

    // Close and cleanup
    void Close();

    // Helper method to create frame key
    std::string CreateFrameKey(int frameIndex) const;

    // Member variables
    std::wstring m_dbPath;
    std::unique_ptr<rocksdb::DB> m_db;

    // Initialization flag
    bool m_initialized;
};

/**
 * RAII helper class for automatic cleanup of temporary metadata databases
 */
class TemporaryFrameMetadataStorage {
public:
    /**
     * Create temporary frame metadata storage
     * @param tempDir Directory for temporary database (empty for system temp)
     */
    TemporaryFrameMetadataStorage(const std::wstring& tempDir = L"");

    /**
     * Destructor - automatically deletes temporary file
     */
    ~TemporaryFrameMetadataStorage();

    /**
     * Get the underlying storage instance
     * @return Pointer to storage instance, nullptr if creation failed
     */
    FrameMetadataStorage* GetStorage() { return m_storage.get(); }

    /**
     * Check if the temporary storage is valid
     * @return True if valid, false otherwise
     */
    bool IsValid() const { return m_storage && m_storage->IsInitialized(); }

    /**
     * Get the temporary database path
     * @return Temporary database path
     */
    const std::wstring& GetTempDbPath() const { return m_tempDbPath; }

private:
    std::unique_ptr<FrameMetadataStorage> m_storage;
    std::wstring m_tempDbPath;
    bool m_deleteOnDestroy;
};
