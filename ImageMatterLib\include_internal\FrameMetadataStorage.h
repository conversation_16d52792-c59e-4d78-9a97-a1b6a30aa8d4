#pragma once

#include <Windows.h>
#include <memory>
#include <string>
#include "FrameMetadata.h"

/**
 * Memory-mapped file storage for frame metadata persistence between processing passes
 * Provides efficient storage and retrieval of frame metadata without excessive RAM usage
 */
class FrameMetadataStorage {
public:
    /**
     * Create or open a memory-mapped file for frame metadata storage
     * @param filePath Path to the memory-mapped file
     * @param maxFrames Maximum number of frames to support
     * @param createNew If true, create a new file (overwriting existing), if false, open existing
     * @return Unique pointer to storage instance, nullptr on failure
     */
    static std::unique_ptr<FrameMetadataStorage> Create(
        const std::wstring& filePath, 
        int maxFrames, 
        bool createNew = true);

    /**
     * Destructor - automatically closes file and unmaps memory
     */
    ~FrameMetadataStorage();

    /**
     * Get frame metadata by index
     * @param frameIndex Frame index (0-based)
     * @return Pointer to frame metadata, nullptr if invalid index
     */
    FrameMetadata* GetFrameMetadata(int frameIndex);

    /**
     * Get frame metadata by index (const version)
     * @param frameIndex Frame index (0-based)
     * @return Const pointer to frame metadata, nullptr if invalid index
     */
    const FrameMetadata* GetFrameMetadata(int frameIndex) const;

    /**
     * Set frame metadata by index
     * @param frameIndex Frame index (0-based)
     * @param metadata Metadata to set
     * @return True on success, false on failure
     */
    bool SetFrameMetadata(int frameIndex, const FrameMetadata& metadata);

    /**
     * Get the maximum number of frames supported
     * @return Maximum frame count
     */
    int GetMaxFrames() const { return m_maxFrames; }

    /**
     * Flush any pending writes to disk
     * @return True on success, false on failure
     */
    bool Flush();

    /**
     * Check if the storage is properly initialized
     * @return True if initialized, false otherwise
     */
    bool IsInitialized() const { return m_mappedMemory != nullptr; }

    /**
     * Get the file path
     * @return File path as wide string
     */
    const std::wstring& GetFilePath() const { return m_filePath; }

    /**
     * Get the total file size in bytes
     * @return File size in bytes
     */
    size_t GetFileSize() const { return m_fileSize; }

private:
    // Private constructor - use Create() method
    FrameMetadataStorage();

    // Initialize the storage
    bool Initialize(const std::wstring& filePath, int maxFrames, bool createNew);

    // Close and cleanup
    void Close();

    // Member variables
    std::wstring m_filePath;
    int m_maxFrames;
    size_t m_fileSize;
    
    // Windows memory mapping handles
    HANDLE m_fileHandle;
    HANDLE m_mappingHandle;
    void* m_mappedMemory;
    
    // Initialization flag
    bool m_initialized;
};

/**
 * RAII helper class for automatic cleanup of temporary metadata files
 */
class TemporaryFrameMetadataStorage {
public:
    /**
     * Create temporary frame metadata storage
     * @param maxFrames Maximum number of frames to support
     * @param tempDir Directory for temporary files (empty for system temp)
     */
    TemporaryFrameMetadataStorage(int maxFrames, const std::wstring& tempDir = L"");

    /**
     * Destructor - automatically deletes temporary file
     */
    ~TemporaryFrameMetadataStorage();

    /**
     * Get the underlying storage instance
     * @return Pointer to storage instance, nullptr if creation failed
     */
    FrameMetadataStorage* GetStorage() { return m_storage.get(); }

    /**
     * Check if the temporary storage is valid
     * @return True if valid, false otherwise
     */
    bool IsValid() const { return m_storage && m_storage->IsInitialized(); }

    /**
     * Get the temporary file path
     * @return Temporary file path
     */
    const std::wstring& GetTempFilePath() const { return m_tempFilePath; }

private:
    std::unique_ptr<FrameMetadataStorage> m_storage;
    std::wstring m_tempFilePath;
    bool m_deleteOnDestroy;
};
