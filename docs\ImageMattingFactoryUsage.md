# ImageMattingFactory - Guide d'Utilisation

## Vue d'ensemble

La `ImageMattingFactory` fournit une interface unifiée pour créer des instances ImageMatting qui détecte automatiquement le format du modèle et utilise l'implémentation appropriée (ONNX Runtime ou TensorRT).

## Fonction Principale : `Init()`

### **Signature**
```cpp
static std::unique_ptr<ImageMattingBase> Init(
    const wchar_t* modelPath,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    bool isRgba = false,
    cudaStream_t externalStream = nullptr
);
```

### **Détection Automatique du Format**

La factory détecte automatiquement le type d'implémentation basé sur l'extension du fichier :

| Extension | Implémentation | Description |
|-----------|----------------|-------------|
| `.onnx` | ONNX Runtime | Modèles ONNX standard |
| `.trt` | TensorRT | Engines TensorRT optimisés |
| `.engine` | TensorRT | Engines TensorRT optimisés |

## Exemples d'Utilisation

### **1. Utilisation Basique**
```cpp
#include "ImageMattingFactory.h"

// Initialisation automatique basée sur l'extension
auto matting = ImageMattingFactory::Init(
    L"models/plus_ultra.onnx",  // Utilisera ONNX Runtime
    1920, 1080,
    NormalizationParams::ImageNet(),
    false,
    stream
);

if (matting) {
    std::cout << "Using: " << matting->GetImplementationType() << std::endl;
    
    // Utilisation identique peu importe l'implémentation
    if (matting->Infer()) {
        float* result = matting->GetOutputBuffer();
        // Traiter le résultat...
    }
}
```

### **2. Avec TensorRT Engine**
```cpp
// Utilisera automatiquement TensorRT
auto tensorRtMatting = ImageMattingFactory::Init(
    L"engines/plus_ultra_1024x1024.trt",  // Utilisera TensorRT
    1920, 1080,
    NormalizationParams::ImageNet(),
    false,
    stream
);
```

### **3. Sélection Automatique d'Engine**
```cpp
// Recherche automatique du meilleur engine TensorRT disponible
auto autoMatting = ImageMattingFactory::InitAuto(
    1920, 1080,
    NormalizationParams::ImageNet(),
    false,
    stream
);
```

## Migration du Code Existant

### **Avant (Code Spécifique)**
```cpp
// Ancien code avec implémentation spécifique
auto imageMatting = std::make_unique<ImageMatting>();
if (!imageMatting->Init(modelPath, imageWidth, imageHeight, normParams, false, stream)) {
    std::cerr << "Failed to initialize" << std::endl;
    return nullptr;
}
return imageMatting;
```

### **Après (Factory)**
```cpp
// Nouveau code avec factory
auto imageMatting = ImageMattingFactory::Init(modelPath, imageWidth, imageHeight, normParams, false, stream);
if (!imageMatting) {
    std::cerr << "Failed to initialize" << std::endl;
    return nullptr;
}
return imageMatting;
```

## Intégration dans les Modules Existants

### **InsPyReNet.cpp**
```cpp
// Avant
auto imageMatting = std::make_unique<ImageMatting>();
if (!imageMatting->Init(bestModel.path.c_str(), imageWidth, imageHeight, 
                       NormalizationParams::ImageNet(), false, stream)) {
    return nullptr;
}

// Après
auto imageMatting = ImageMattingFactory::Init(bestModel.path.c_str(), imageWidth, imageHeight, 
                                            NormalizationParams::ImageNet(), false, stream);
if (!imageMatting) {
    return nullptr;
}
```

### **FrameProcessor.cpp**
```cpp
// Utilisation dans FrameProcessor (inchangée grâce au polymorphisme)
initialImageMatter = InitInsPyReNet(videoWidth, videoHeight, processingStream);
if (!initialImageMatter) {
    std::cerr << "Failed to initialize InsPyReNet" << std::endl;
    return false;
}

// L'utilisation reste identique
if (!initialImageMatter->Infer()) {
    std::cerr << "Failed to run inference" << std::endl;
    return false;
}
```

## Avantages de la Factory

### **🔄 Simplicité d'Utilisation**
- **Une seule fonction** pour tous les types de modèles
- **Détection automatique** du format
- **Interface unifiée** pour ONNX et TensorRT

### **🚀 Performance Optimale**
- **TensorRT automatique** quand disponible
- **Fallback ONNX** si TensorRT échoue
- **Engines optimisés** sélectionnés automatiquement

### **🛠️ Maintenance Simplifiée**
- **Code unifié** dans tous les modules
- **Polymorphisme** : même interface pour toutes les implémentations
- **Évolutivité** : nouvelles implémentations ajoutées facilement

## Fonctions Auxiliaires

### **InitAuto() - Sélection Automatique**
```cpp
// Recherche le meilleur engine TensorRT ou fallback vers ONNX
auto matting = ImageMattingFactory::InitAuto(1920, 1080, normParams, false, stream);
```

### **InitOnnx() - Force ONNX Runtime**
```cpp
// Force l'utilisation d'ONNX Runtime
auto onnxMatting = ImageMattingFactory::InitOnnx(L"model.onnx", 1920, 1080, normParams, false, stream);
```

### **InitTensorRt() - Force TensorRT**
```cpp
// Force l'utilisation de TensorRT
auto tensorRtMatting = ImageMattingFactory::InitTensorRt(L"engine.trt", 1920, 1080, normParams, false, stream);
```

## Gestion d'Erreurs

### **Vérification de Succès**
```cpp
auto matting = ImageMattingFactory::Init(modelPath, width, height, normParams);
if (!matting) {
    std::cerr << "Failed to initialize ImageMatting" << std::endl;
    // Gérer l'erreur...
    return false;
}
```

### **Messages d'Erreur Détaillés**
La factory affiche automatiquement des messages informatifs :
```
Initializing ImageMatting with file: models/plus_ultra.onnx
Detected extension: .onnx
Using ONNX Runtime implementation
ONNX Runtime implementation initialized successfully
```

## Compatibilité

### **✅ Interface Inchangée**
- **Polymorphisme** : `ImageMattingBase*` utilisé partout
- **Méthodes identiques** : `Infer()`, `GetInputBuffer()`, etc.
- **Compatibilité totale** avec le code existant

### **✅ Performance Préservée**
- **Aucun overhead** de la factory
- **Optimisations** préservées dans chaque implémentation
- **Sélection optimale** automatique

## Exemple Complet

```cpp
#include "ImageMattingFactory.h"

bool ProcessVideo(const wchar_t* modelPath, int width, int height) {
    // Créer stream CUDA
    cudaStream_t stream;
    cudaStreamCreate(&stream);
    
    // Initialiser avec factory (détection automatique)
    auto matting = ImageMattingFactory::Init(
        modelPath, width, height,
        NormalizationParams::ImageNet(),
        false, stream
    );
    
    if (!matting) {
        std::cerr << "Failed to initialize ImageMatting" << std::endl;
        return false;
    }
    
    std::cout << "Successfully initialized " << matting->GetImplementationType() 
              << " implementation" << std::endl;
    
    // Traitement des frames
    for (int frame = 0; frame < numFrames; frame++) {
        // Copier données d'entrée
        cudaMemcpy(matting->GetInputBuffer(), frameData, 
                  matting->GetInputBufferSize(), cudaMemcpyHostToDevice);
        
        // Inférence
        if (matting->Infer()) {
            // Récupérer résultats
            float* output = matting->GetOutputBuffer();
            // Traiter output...
        }
    }
    
    // Cleanup
    matting->Shutdown();
    cudaStreamDestroy(stream);
    
    return true;
}
```

Cette factory simplifie grandement l'utilisation d'ImageMatting tout en offrant la flexibilité de choisir l'implémentation optimale automatiquement ! 🚀
