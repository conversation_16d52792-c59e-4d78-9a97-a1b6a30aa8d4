// VideoWriterAlpha.cpp
#include "VideoWriterAlpha.h"
#include "main_Kernels.cuh"
#include <cuda.h>  // CUDA Driver API

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/hwcontext.h>
#include <libavutil/hwcontext_cuda.h>
#include <libavutil/error.h>
#include <libavutil/imgutils.h>
#include <libavutil/opt.h>
#include <libswscale/swscale.h>
}

#include <stdexcept>
#include <iostream>
#include <algorithm>
#include <cmath>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <chrono>

#pragma comment(lib, "avcodec.lib")
#pragma comment(lib, "avformat.lib")
#pragma comment(lib, "avutil.lib")
#pragma comment(lib, "swscale.lib")
#pragma comment(lib, "cuda.lib")
#pragma comment(lib, "cudart.lib")

// Helper to check for CUDA errors
static void CheckCudaError(cudaError_t error)
{
    if (error != cudaSuccess) {
        throw std::runtime_error(std::string("CUDA error: ") + cudaGetErrorString(error));
    }
}

// Helper to convert wstring to string
static std::string WStringToString(const std::wstring& wstr)
{
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

// Helper to create AVRational
static AVRational* CreateAVRational(int num, int den)
{
    AVRational* r = new AVRational;
    r->num = num;
    r->den = den;
    return r;
}

// Helper to normalize AVRational to reasonable values for encoding
static AVRational NormalizeFrameRate(const AVRational& frameRate) {
    // Convert to double and back to get a reasonable fraction
    double fps = static_cast<double>(frameRate.num) / frameRate.den;

    // For common frame rates, use exact values
    if (abs(fps - 23.976) < 0.001) return {24000, 1001};
    if (abs(fps - 24.0) < 0.001) return {24, 1};
    if (abs(fps - 25.0) < 0.001) return {25, 1};
    if (abs(fps - 29.97) < 0.001) return {30000, 1001};
    if (abs(fps - 30.0) < 0.001) return {30, 1};
    if (abs(fps - 50.0) < 0.001) return {50, 1};
    if (abs(fps - 59.94) < 0.001) return {60000, 1001};
    if (abs(fps - 60.0) < 0.001) return {60, 1};

    // For other frame rates, use a simpler approach to avoid large fractions
    int roundedFps = static_cast<int>(fps * 1000 + 0.5);

    // Use a smaller denominator to avoid huge time_base values
    int denominators[] = {1, 2, 4, 5, 8, 10, 25, 50, 100, 1000};

    for (int den : denominators) {
        int num = static_cast<int>(fps * den + 0.5);
        double testFps = static_cast<double>(num) / den;
        if (abs(testFps - fps) < 0.01) { // Within 0.01 fps tolerance
            return {num, den};
        }
    }

    // Fallback: use a reasonable fraction with limited precision
    return {static_cast<int>(fps * 100 + 0.5), 100};
}

// Helper function for FFmpeg error handling
static void CheckFFmpegError(int error) {
    if (error < 0) {
        char errBuf[AV_ERROR_MAX_STRING_SIZE] = { 0 };
        av_strerror(error, errBuf, AV_ERROR_MAX_STRING_SIZE);
        throw std::runtime_error(std::string("FFmpeg error: ") + errBuf);
    }
}

// Constructor
VideoWriterAlpha::VideoWriterAlpha()
    : m_formatContext(nullptr)
    , m_codecContext(nullptr)
    , m_videoStream(nullptr)
    , m_swFrame(nullptr)
    , m_hwFrame(nullptr)
    , m_packet(nullptr)
    , m_cudaContext(nullptr)
    , m_cudaStream(0)
    , m_cudaYuvaBuffer(nullptr)
    , m_cudaYuvaBufferSize(0)
    , m_width(0)
    , m_height(0)
    , m_bitRate(0)
    , m_frameRate(nullptr)
    , m_gopSize(0)
    , m_pts(0)
    , m_isInitialized(false)
    , m_isFinalized(false)
    , m_useHardwareAcceleration(false)
{
}

// Destructor
VideoWriterAlpha::~VideoWriterAlpha()
{
    Close();
}

// Create a new alpha video writer
std::unique_ptr<VideoWriterAlpha> VideoWriterAlpha::Create(const std::wstring& videoPath, OutputConfig& config, CUcontext cudaContext)
{
    std::unique_ptr<VideoWriterAlpha> writer(new VideoWriterAlpha());
    if (writer->Initialize(videoPath, config, cudaContext)) {
        return writer;
    }
    return nullptr;
}

// Initialize the alpha video writer
bool VideoWriterAlpha::Initialize(const std::wstring& videoPath, OutputConfig& config, CUcontext cudaContext)
{
    try {
        m_cudaContext = cudaContext;
        if (!m_cudaContext) {
            throw std::runtime_error("A valid CUDA context must be provided.");
        }

        CheckCudaError(cudaStreamCreate(&m_cudaStream));

        std::string filePath = WStringToString(videoPath);
        if (avformat_alloc_output_context2(&m_formatContext, nullptr, nullptr, filePath.c_str()) < 0) {
            throw std::runtime_error("Could not allocate output format context.");
        }

        const AVCodec* encoder = avcodec_find_encoder_by_name(config.encoder.c_str());
        if (!encoder) {
            throw std::runtime_error("Could not find encoder: " + config.encoder);
        }

        std::cout << "VideoWriterAlpha: Using encoder: " << config.encoder << std::endl;

        m_videoStream = avformat_new_stream(m_formatContext, encoder);
        if (!m_videoStream) {
            throw std::runtime_error("Could not create video stream.");
        }

        m_codecContext = avcodec_alloc_context3(encoder);
        if (!m_codecContext) {
            throw std::runtime_error("Could not allocate codec context.");
        }

        m_width = config.width;
        m_height = config.height;
        m_bitRate = config.bitRate;
        m_gopSize = config.gopSize;
        m_pixelFormat = config.pixelFormat;

        // Normalize the frame rate
        AVRational normalizedFrameRate = NormalizeFrameRate(config.frameRate);
        m_frameRate = CreateAVRational(normalizedFrameRate.num, normalizedFrameRate.den);

        // Set codec parameters
        m_codecContext->width = m_width;
        m_codecContext->height = m_height;
        m_codecContext->bit_rate = m_bitRate;
        m_codecContext->gop_size = m_gopSize;
        m_codecContext->max_b_frames = 0;

        // Set pixel format for alpha support
        if (config.pixelFormat == "yuva444p10le") {
            m_codecContext->pix_fmt = AV_PIX_FMT_YUVA444P10LE;
        } else if (config.pixelFormat == "yuva420p") {
            m_codecContext->pix_fmt = AV_PIX_FMT_YUVA420P;
        } else if (config.pixelFormat == "rgba") {
            m_codecContext->pix_fmt = AV_PIX_FMT_RGBA;
        } else {
            // Default to YUVA444P10LE for ProRes 4444
            m_codecContext->pix_fmt = AV_PIX_FMT_YUVA444P10LE;
        }

        // Use a standard time_base
        AVRational timeBase = {1, 90000};  // 90kHz time base
        m_codecContext->time_base = timeBase;
        m_codecContext->framerate = *m_frameRate;

        std::cout << "VideoWriterAlpha: Using time_base: " << timeBase.num << "/" << timeBase.den << std::endl;
        std::cout << "VideoWriterAlpha: Using pixel format: " << config.pixelFormat << std::endl;

        // Set encoder-specific options
        AVDictionary* options = nullptr;

        if (config.encoder == "prores_ks") {
            // ProRes specific settings
            if (config.IsProfileSet()) {
                av_dict_set(&options, "profile", config.GetProfile().c_str(), 0);
            }
            av_dict_set(&options, "vendor", "apl0", 0);  // Apple vendor code
        } else if (config.encoder == "ffv1") {
            // FFV1 specific settings for lossless compression
            av_dict_set(&options, "level", "3", 0);
            av_dict_set(&options, "coder", "1", 0);
        }

        // Apply additional encoder options
        for (const auto& option : config.GetEncoderOptions()) {
            av_dict_set(&options, option.first.c_str(), option.second.c_str(), 0);
        }

        // Open codec
        CheckFFmpegError(avcodec_open2(m_codecContext, encoder, &options));
        av_dict_free(&options);

        // Copy codec parameters to stream
        CheckFFmpegError(avcodec_parameters_from_context(m_videoStream->codecpar, m_codecContext));

        // Allocate frame
        m_swFrame = av_frame_alloc();
        if (!m_swFrame) {
            throw std::runtime_error("Could not allocate frame.");
        }

        m_swFrame->format = m_codecContext->pix_fmt;
        m_swFrame->width = m_width;
        m_swFrame->height = m_height;

        CheckFFmpegError(av_frame_get_buffer(m_swFrame, 32));

        // Allocate packet
        m_packet = av_packet_alloc();
        if (!m_packet) {
            throw std::runtime_error("Could not allocate packet.");
        }

        // Allocate CUDA buffer for YUVA conversion
        // For 10-bit YUVA444, each component is 2 bytes (16-bit storage)
        size_t ySize = m_width * m_height * 2; // 2 bytes per pixel for 10-bit
        size_t uvSize = ySize; // For 444 format, U and V have same size as Y
        size_t aSize = ySize;  // Alpha has same size as Y
        m_cudaYuvaBufferSize = ySize + uvSize + uvSize + aSize; // Y + U + V + A

        CheckCudaError(cudaMalloc(&m_cudaYuvaBuffer, m_cudaYuvaBufferSize));

        // Open output file
        if (!(m_formatContext->oformat->flags & AVFMT_NOFILE)) {
            if (avio_open(&m_formatContext->pb, filePath.c_str(), AVIO_FLAG_WRITE) < 0) {
                throw std::runtime_error("Could not open output file: " + filePath);
            }
        }

        if (avformat_write_header(m_formatContext, nullptr) < 0) {
            throw std::runtime_error("Could not write video header.");
        }

        // Start asynchronous encoding thread
        m_encodingThread = std::thread(&VideoWriterAlpha::EncodingThreadFunction, this);

        m_isInitialized = true;
        std::cout << "VideoWriterAlpha: Initialized with asynchronous encoding" << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error initializing VideoWriterAlpha: " << e.what() << std::endl;
        Close();
        return false;
    }
}

// Asynchronous encoding thread function
void VideoWriterAlpha::EncodingThreadFunction() {
    // Set CUDA context for this thread
    CUresult contextResult = cuCtxSetCurrent(m_cudaContext);
    if (contextResult != CUDA_SUCCESS) {
        std::cerr << "ERROR: Failed to set CUDA context in encoding thread: " << contextResult << std::endl;
        m_encodingError = true;
        return;
    }

    std::cout << "VideoWriterAlpha: Encoding thread started" << std::endl;

    while (true) {
        std::unique_lock<std::mutex> lock(m_queueMutex);

        // Debug: Show queue state before waiting
        // std::cout << "Encoding thread: Waiting for frames (queue size: " << m_frameQueue.size() << ")" << std::endl;

        m_queueCondition.wait(lock, [this] {
            return !m_frameQueue.empty() || m_stopEncoding.load();
        });

        if (m_frameQueue.empty() && m_stopEncoding.load()) {
            break;
        }

        if (!m_frameQueue.empty()) {
            FrameData frameData = m_frameQueue.front();
            m_frameQueue.pop();
            lock.unlock();

            if (frameData.isLastFrame) {
                break;
            }

            try {
                // Process the frame
                if (!PrepareFrame(frameData.pts)) {
                    std::cerr << "VideoWriterAlpha: Failed to prepare frame" << std::endl;
                    m_encodingError = true;
                    break;
                }

                if (!TransferRgbaToFrame(frameData.cudaRgbaBuffer, frameData.bufferSize)) {
                    std::cerr << "VideoWriterAlpha: Failed to transfer RGBA to frame" << std::endl;
                    m_encodingError = true;
                    break;
                }

                if (!EncodeFrame()) {
                    std::cerr << "VideoWriterAlpha: Failed to encode frame" << std::endl;
                    m_encodingError = true;
                    break;
                }
            }
            catch (const std::exception& e) {
                std::cerr << "VideoWriterAlpha: Error in encoding thread: " << e.what() << std::endl;
                m_encodingError = true;
                break;
            }
        }
    }

    std::cout << "VideoWriterAlpha: Encoding thread finished" << std::endl;
}

// Write a frame asynchronously
bool VideoWriterAlpha::WriteFrame(void* cudaRgbaBuffer, size_t bufferSize) {
    if (!m_isInitialized || !cudaRgbaBuffer || m_isFinalized) return false;

    // Check for encoding errors first
    if (m_encodingError.load()) {
        std::cerr << "VideoWriterAlpha: Encoding error detected, stopping frame submission" << std::endl;
        return false;
    }

    // Simple approach: if queue is full, wait briefly and try again
    while (true) {
        {
            std::unique_lock<std::mutex> lock(m_queueMutex);

            // If queue has space, add the frame
            if (m_frameQueue.size() < MAX_QUEUE_SIZE) {
                // Calculate PTS for this frame
                double fps = static_cast<double>(m_frameRate->num) / m_frameRate->den;
                int64_t ptsIncrement = static_cast<int64_t>(90000.0 / fps + 0.5);
                int64_t currentPts = m_pts;
                m_pts += ptsIncrement;

                // Add frame to encoding queue
                m_frameQueue.emplace(cudaRgbaBuffer, bufferSize, currentPts);

                // Notify encoding thread
                m_queueCondition.notify_one();
                return true;
            }
        }

        // Queue is full, wait a bit for encoding thread to process frames
        std::this_thread::sleep_for(std::chrono::milliseconds(1));

        // Check for errors again
        if (m_encodingError.load()) {
            std::cerr << "VideoWriterAlpha: Encoding error detected while waiting" << std::endl;
            return false;
        }
    }
}

// Get current queue size
size_t VideoWriterAlpha::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(m_queueMutex);
    return m_frameQueue.size();
}

bool VideoWriterAlpha::PrepareFrame(int64_t pts) {
    m_swFrame->pts = pts;
    return true;
}

bool VideoWriterAlpha::TransferRgbaToFrame(void* cudaRgbaBuffer, size_t bufferSize) {
    if (!cudaRgbaBuffer || !m_swFrame) return false;

    try {
        // Convert RGBA to YUVA format on GPU
        size_t rgbaSize = m_width * m_height * 4 * sizeof(float); // Assuming float RGBA
        if (bufferSize < rgbaSize) {
            std::cerr << "Buffer size too small for RGBA data" << std::endl;
            return false;
        }

        // Convert RGBA to YUVA444 format using our CUDA kernel
        launchRgbaToYuva444(static_cast<float*>(cudaRgbaBuffer), static_cast<unsigned char*>(m_cudaYuvaBuffer), m_width, m_height, m_cudaStream);

        // Copy converted YUVA444 data to frame
        size_t componentSize = m_width * m_height * 2; // 2 bytes per pixel for 10-bit

        // Copy Y plane (10-bit)
        CheckCudaError(cudaMemcpy2DAsync(
            m_swFrame->data[0], m_swFrame->linesize[0],
            m_cudaYuvaBuffer, m_width * 2, // 2 bytes per pixel
            m_width * 2, m_height,
            cudaMemcpyDeviceToHost, m_cudaStream
        ));

        // Copy U plane (10-bit)
        CheckCudaError(cudaMemcpy2DAsync(
            m_swFrame->data[1], m_swFrame->linesize[1],
            static_cast<char*>(m_cudaYuvaBuffer) + componentSize, m_width * 2,
            m_width * 2, m_height,
            cudaMemcpyDeviceToHost, m_cudaStream
        ));

        // Copy V plane (10-bit)
        CheckCudaError(cudaMemcpy2DAsync(
            m_swFrame->data[2], m_swFrame->linesize[2],
            static_cast<char*>(m_cudaYuvaBuffer) + 2 * componentSize, m_width * 2,
            m_width * 2, m_height,
            cudaMemcpyDeviceToHost, m_cudaStream
        ));

        // Copy Alpha plane (10-bit)
        CheckCudaError(cudaMemcpy2DAsync(
            m_swFrame->data[3], m_swFrame->linesize[3],
            static_cast<char*>(m_cudaYuvaBuffer) + 3 * componentSize, m_width * 2,
            m_width * 2, m_height,
            cudaMemcpyDeviceToHost, m_cudaStream
        ));

        CheckCudaError(cudaStreamSynchronize(m_cudaStream));
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error transferring RGBA to frame: " << e.what() << std::endl;
        return false;
    }
}

bool VideoWriterAlpha::EncodeFrame() {
    int ret = avcodec_send_frame(m_codecContext, m_swFrame);
    if (ret < 0) {
        char err_buf[AV_ERROR_MAX_STRING_SIZE];
        av_make_error_string(err_buf, AV_ERROR_MAX_STRING_SIZE, ret);
        std::cerr << "Error sending frame to encoder: " << err_buf << std::endl;
        return false;
    }

    while (ret >= 0) {
        ret = avcodec_receive_packet(m_codecContext, m_packet);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) break;
        if (ret < 0) return false;
        if (av_interleaved_write_frame(m_formatContext, m_packet) < 0) {
            throw std::runtime_error("Error writing packet to file.");
        }
        av_packet_unref(m_packet);
    }
    return true;
}

bool VideoWriterAlpha::Finalize() {
    if (!m_isInitialized || m_isFinalized) return false;

    std::cout << "VideoWriterAlpha: Finalizing - waiting for encoding thread to complete..." << std::endl;

    // Signal encoding thread to stop and wait for it to finish
    m_stopEncoding = true;
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_frameQueue.emplace(nullptr, 0, 0, true); // Last frame marker
    }
    m_queueCondition.notify_one();

    if (m_encodingThread.joinable()) {
        m_encodingThread.join();
    }

    if (m_encodingError.load()) {
        std::cerr << "VideoWriterAlpha: Encoding errors occurred during processing" << std::endl;
        return false;
    }

    // Ensure all CUDA operations are complete
    if (m_cudaStream) cudaStreamSynchronize(m_cudaStream);

    // Flush encoder
    int ret = avcodec_send_frame(m_codecContext, nullptr);
    if (ret < 0) return false;

    while (ret >= 0) {
        ret = avcodec_receive_packet(m_codecContext, m_packet);
        if (ret == AVERROR_EOF) break;
        if (ret < 0) return false;
        if (av_interleaved_write_frame(m_formatContext, m_packet) < 0) return false;
        av_packet_unref(m_packet);
    }

    if (av_write_trailer(m_formatContext) < 0) return false;

    m_isFinalized = true;
    std::cout << "VideoWriterAlpha: Finalization complete" << std::endl;
    return true;
}

void VideoWriterAlpha::Close() {
    // Stop encoding thread if still running
    if (!m_stopEncoding.load()) {
        m_stopEncoding = true;
        m_queueCondition.notify_all();
        if (m_encodingThread.joinable()) {
            m_encodingThread.join();
        }
    }

    if (m_cudaYuvaBuffer) {
        cudaFree(m_cudaYuvaBuffer);
        m_cudaYuvaBuffer = nullptr;
    }
    if (m_cudaStream) {
        cudaStreamDestroy(m_cudaStream);
        m_cudaStream = nullptr;
    }
    if (m_packet) {
        av_packet_free(&m_packet);
    }
    if (m_swFrame) {
        av_frame_free(&m_swFrame);
    }
    if (m_hwFrame) {
        av_frame_free(&m_hwFrame);
    }
    if (m_codecContext) {
        avcodec_free_context(&m_codecContext);
    }
    if (m_formatContext) {
        if (!(m_formatContext->oformat->flags & AVFMT_NOFILE)) {
            avio_closep(&m_formatContext->pb);
        }
        avformat_free_context(m_formatContext);
        m_formatContext = nullptr;
    }
    if (m_frameRate) {
        delete m_frameRate;
        m_frameRate = nullptr;
    }
    m_isInitialized = false;
    m_isFinalized = false;
}
