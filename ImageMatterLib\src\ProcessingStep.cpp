#include "ProcessingStep.h"
#include "FindInitialAlphaStep.h"
#include "DetectHeadsStep.h"
#include "ProcessBodyRegionsStep.h"
#include "ProcessHeadRegionsStep.h"
#include <iostream>

// Factory method implementation
std::unique_ptr<ProcessingStep> ProcessingStepFactory::CreateStep(StepType stepType) {
    switch (stepType) {
        case StepType::FINAL_INITIAL_ALPHA:
            return std::make_unique<FindInitialAlphaStep>();
        case StepType::DETECT_HEADS:
            return std::make_unique<DetectHeadsStep>();
        case StepType::PROCESS_BODY_REGIONS:
            return std::make_unique<ProcessBodyRegionsStep>();
        case StepType::PROCESS_HEAD_REGIONS:
            // Note: For head regions, use CreateHeadRegionsStep() instead to specify model size
            std::cerr << "Use CreateHeadRegionsStep() for head regions processing" << std::endl;
            return nullptr;
        default:
            std::cerr << "Unknown step type: " << static_cast<int>(stepType) << std::endl;
            return nullptr;
    }
}

// Create head regions processing step for a specific model size
std::unique_ptr<ProcessingStep> ProcessingStepFactory::CreateHeadRegionsStep(int headModelSize) {
    return std::make_unique<ProcessHeadRegionsStep>(headModelSize);
}

// Get step type name for logging
const char* ProcessingStepFactory::GetStepTypeName(StepType stepType) {
    switch (stepType) {
        case StepType::FINAL_INITIAL_ALPHA:
            return "FindInitialAlpha";
        case StepType::DETECT_HEADS:
            return "DetectHeads";
        case StepType::PROCESS_BODY_REGIONS:
            return "ProcessBodyRegions";
        case StepType::PROCESS_HEAD_REGIONS:
            return "ProcessHeadRegions";
        default:
            return "Unknown";
    }
}
