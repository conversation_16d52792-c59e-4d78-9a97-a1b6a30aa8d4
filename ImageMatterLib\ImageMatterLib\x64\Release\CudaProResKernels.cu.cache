Identity=src\CudaProResKernels.cu
AdditionalCompilerOptions=
AdditionalCompilerOptions=
AdditionalDependencies=
AdditionalDeps=
AdditionalLibraryDirectories=
AdditionalOptions=--expt-relaxed-constexpr
AdditionalOptions=--expt-relaxed-constexpr
CodeGeneration=compute_52,sm_52
CodeGeneration=compute_52,sm_52
CompileOut=f:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\ImageMatterLib\x64\Release\CudaProResKernels.cu.obj
CudaRuntime=Static
CudaToolkitCustomDir=
DebugInformationFormat=None
DebugInformationFormat=None
Defines=;IMAGEMATTERLIB_EXPORTS;_CRT_SECURE_NO_WARNINGS;_WINDLL;_UNICODE;UNICODE;
Emulation=false
EnableVirtualArchInFatbin=true
ExtensibleWholeProgramCompilation=false
FastCompile=Off
FastMath=false
GenerateLineInfo=false
GenerateRelocatableDeviceCode=false
GPUDebugInfo=false
GPUDebugInfo=false
HostDebugInfo=false
Include=;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include;F:\VccLibs\ffmpeg\include;F:\VccLibs\onnxruntime\include;F:\VccLibs\TensorRT\include;F:\VccLibs\rocksdb\include;;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include
Inputs=
InterleaveSourceInPTX=false
Keep=false
KeepDir=ImageMatterLib\x64\Release
LinkOut=
MaxRegCount=0
NvccCompilation=compile
NvccPath=
Optimization=O2
Optimization=O2
PerformDeviceLink=
PerformDeviceLinkTimeOptimization=
PtxAsOptionV=false
RequiredIncludes=
Runtime=MD
Runtime=MD
RuntimeChecks=Default
RuntimeChecks=Default
SplitCompile=Default
SplitCompileCustomThreads=
TargetMachinePlatform=64
TargetMachinePlatform=64
TypeInfo=
TypeInfo=
UseHostDefines=true
UseHostInclude=true
UseHostLibraryDependencies=
UseHostLibraryDirectories=
Warning=W3
Warning=W3
