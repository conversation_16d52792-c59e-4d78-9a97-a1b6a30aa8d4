#include "ImageMattingFactory.h"
#include "ImageMatting.h"
#include "ImageMattingOnnx.h"
#include "ImageMattingTensorRt.h"
#include "Helpers.h"
#include <NvInfer.h>
#include <NvOnnxParser.h>
#include <iostream>
#include <filesystem>
#include <algorithm>
#include <regex>
#include <mutex>
#include <fstream>
#include <minwindef.h>
#include <libloaderapi.h>
#include <vector>
#include <chrono>
#include <thread>
#include <iomanip>

// Static member definitions
std::map<ModelType, ModelTypeConfig> ImageMattingFactory::s_modelConfigs;
std::vector<ModelInfo> ImageMattingFactory::s_availableModels;
std::map<std::tuple<ModelType, int, int, int, int, InferenceBackend, cudaStream_t>, std::vector<std::unique_ptr<MattingInstance>>> ImageMattingFactory::s_instances;
std::mutex ImageMattingFactory::s_instancesMutex;
bool ImageMattingFactory::s_modelsScanned = false;
std::mutex ImageMattingFactory::s_scanMutex;



// NEW UNIFIED INTERFACE IMPLEMENTATIONS

ImageMatting* ImageMattingFactory::GetInstance(
    ModelType modelType,
    int imageWidth,
    int imageHeight,
    cudaStream_t stream,
    InferenceBackend backend,
    BestModelSelectionMethod selectionMethod,
    bool useModelSize) {

    // Initialize model configs if not done yet
    InitializeModelConfigs();

    // Get default normalization for this model type
    auto configIt = s_modelConfigs.find(modelType);
    if (configIt == s_modelConfigs.end()) {
        std::cerr << "Error: Unknown model type" << std::endl;
        return nullptr;
    }

    return GetInstance(modelType, imageWidth, imageHeight, configIt->second.defaultNormalization, stream, backend, selectionMethod, useModelSize);
}

std::unique_ptr<ImageMatting> ImageMattingFactory::Init(
    ModelType modelType,
    int imageWidth,
    int imageHeight,
    cudaStream_t stream,
    InferenceBackend backend,
    BestModelSelectionMethod selectionMethod,
    bool useModelSize) {

    // Initialize model configs if not done yet
    InitializeModelConfigs();

    // Get default normalization for this model type
    auto configIt = s_modelConfigs.find(modelType);
    if (configIt == s_modelConfigs.end()) {
        std::cerr << "Error: Unknown model type" << std::endl;
        return nullptr;
    }

    return Init(modelType, imageWidth, imageHeight, configIt->second.defaultNormalization, stream, backend, selectionMethod, useModelSize);
}

ImageMatting* ImageMattingFactory::GetInstance(
    ModelType modelType,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    cudaStream_t stream,
    InferenceBackend backend,
    BestModelSelectionMethod selectionMethod,
    bool useModelSize) {

    try {
        // Initialize model configs if not done yet
        InitializeModelConfigs();

        // Scan models if not done yet
        ScanAvailableModels();

        // Find the best model for these dimensions
        ModelInfo bestModel = FindBestModel(modelType, imageWidth, imageHeight, selectionMethod, backend);
        if (bestModel.path.empty()) {
            std::cerr << "Error: Could not find a suitable model for type " << static_cast<int>(modelType)
                      << " and dimensions " << imageWidth << "x" << imageHeight << std::endl;
            return nullptr;
        }

        // If useModelSize is true, override imageWidth/imageHeight with model size
        if (useModelSize) {
            imageWidth = bestModel.width;
            imageHeight = bestModel.height;
        }

        // Create key for instance management (include modelType, model size, image size, backend, stream)
        auto key = std::make_tuple(modelType, bestModel.width, bestModel.height, imageWidth, imageHeight, backend, stream);

        // Try to find an existing available instance
        {
            std::lock_guard<std::mutex> lock(s_instancesMutex);

            auto it = s_instances.find(key);
            if (it != s_instances.end()) {
                // Look for an available instance
                for (auto& instance : it->second) {
                    if (!instance->inUse) {
                        instance->inUse = true;
                        std::cout << "Reusing existing model instance: " << bestModel.width << "x" << bestModel.height
                                  << ", image: " << imageWidth << "x" << imageHeight
                                  << ", backend: " << static_cast<int>(backend) << std::endl;
                        return instance->imageMatting.get();
                    }
                }
            }
        }

        // No available instance found, create a new one
        std::cout << "Creating new model instance: " << bestModel.width << "x" << bestModel.height
                  << ", image: " << imageWidth << "x" << imageHeight
                  << ", backend: " << static_cast<int>(backend) << std::endl;

        auto instance = std::make_unique<MattingInstance>();
        instance->type = modelType;
        instance->width = bestModel.width;
        instance->height = bestModel.height;
        instance->inUse = true;
        // Store the stream for debugging/validation
        // (If MattingInstance does not have a stream member, add one)
        instance->stream = stream;

        // Create the ImageMatting instance
        instance->imageMatting = CreateInstance(bestModel, imageWidth, imageHeight, normParams, stream, backend);
        if (!instance->imageMatting) {
            std::cerr << "Failed to create ImageMatting instance" << std::endl;
            return nullptr;
        }

        ImageMatting* result = instance->imageMatting.get();

        // Add the instance to the map
        {
            std::lock_guard<std::mutex> lock(s_instancesMutex);

            auto it = s_instances.find(key);
            if (it == s_instances.end()) {
                s_instances[key] = std::vector<std::unique_ptr<MattingInstance>>();
                it = s_instances.find(key);
            }
            it->second.push_back(std::move(instance));
        }

        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in ImageMattingFactory::Init: " << e.what() << std::endl;
        return nullptr;
    }
}

void ImageMattingFactory::ReturnInstance(ImageMatting* instance) {
    if (!instance) return;

    std::lock_guard<std::mutex> lock(s_instancesMutex);

    // Find the instance and mark it as not in use
    for (auto& pair : s_instances) {
        for (auto& inst : pair.second) {
            if (inst->imageMatting.get() == instance) {
                inst->inUse = false;
                return;
            }
        }
    }

    std::cerr << "Warning: Attempted to return unknown instance" << std::endl;
}

std::unique_ptr<ImageMatting> ImageMattingFactory::Init(
    ModelType modelType,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    cudaStream_t stream,
    InferenceBackend backend,
    BestModelSelectionMethod selectionMethod,
    bool useModelSize) {

    try {
        // Initialize model configs if not done yet
        InitializeModelConfigs();

        // Scan models if not done yet
        ScanAvailableModels();

        // Find the best model for these dimensions
        ModelInfo bestModel = FindBestModel(modelType, imageWidth, imageHeight, selectionMethod, backend);
        if (bestModel.path.empty()) {
            std::cerr << "Error: Could not find a suitable model for type " << static_cast<int>(modelType)
                      << " and dimensions " << imageWidth << "x" << imageHeight << std::endl;
            return nullptr;
        }

        // If useModelSize is true, override imageWidth/imageHeight with model size
        if (useModelSize) {
            imageWidth = bestModel.width;
            imageHeight = bestModel.height;
        }

        std::cout << "Creating new unmanaged model instance: " << bestModel.width << "x" << bestModel.height << std::endl;

        // Create the ImageMatting instance directly (not managed by factory)
        return CreateInstance(bestModel, imageWidth, imageHeight, normParams, stream, backend);
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in ImageMattingFactory::Init: " << e.what() << std::endl;
        return nullptr;
    }
}

std::unique_ptr<ImageMatting> ImageMattingFactory::InitOnnx(
    const wchar_t* modelPath,
    int modelWidth,
    int modelHeight,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    bool isRgba,
    ResizeMethod resizeMethod,
    cudaStream_t externalStream) {

    try {
        auto implementation = std::make_unique<ImageMattingOnnx>();

        if (implementation->Init(modelPath, modelWidth, modelHeight, imageWidth, imageHeight, normParams, isRgba, resizeMethod, externalStream)) {
            std::cout << "ONNX Runtime implementation initialized successfully" << std::endl;
            return std::move(implementation);
        } else {
            std::cerr << "Failed to initialize ONNX Runtime implementation" << std::endl;
            return nullptr;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Exception creating ONNX implementation: " << e.what() << std::endl;
        return nullptr;
    }
}

std::unique_ptr<ImageMatting> ImageMattingFactory::InitTensorRt(
    const wchar_t* enginePath,
    int modelWidth,
    int modelHeight,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    bool isRgba,
    ResizeMethod resizeMethod,
    cudaStream_t externalStream) {

    try {
        auto implementation = std::make_unique<ImageMattingTensorRt>();

        if (implementation->Init(enginePath, modelWidth, modelHeight, imageWidth, imageHeight, normParams, isRgba, resizeMethod, externalStream)) {
            std::cout << "TensorRT implementation initialized successfully" << std::endl;
            return std::move(implementation);
        } else {
            std::wcerr << L"Failed to initialize TensorRT implementation with engine: " << enginePath << std::endl;
            return nullptr;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Exception creating TensorRT implementation: " << e.what() << std::endl;
        return nullptr;
    }
}

std::wstring ImageMattingFactory::GetFileExtension(const wchar_t* filePath) {
    std::wstring path(filePath);
    size_t dotPos = path.find_last_of(L'.');
    if (dotPos != std::wstring::npos) {
        return path.substr(dotPos);
    }
    return L"";
}

bool ImageMattingFactory::FileExists(const wchar_t* filePath) {
    try {
        return std::filesystem::exists(filePath);
    }
    catch (...) {
        return false;
    }
}

bool ImageMattingFactory::CreateTensorRtEngine(const wchar_t* onnxPath, const wchar_t* enginePath, int poolSizeMB, const std::vector<std::string>& additionalArgs) {
    return CreateTensorRtEngine(onnxPath, enginePath, poolSizeMB, additionalArgs, false);
}

bool ImageMattingFactory::CreateTensorRtEngine(const wchar_t* onnxPath, const wchar_t* enginePath, int poolSizeMB, const std::vector<std::string>& additionalArgs, bool isMixedPrecision) {
    try {
        std::cout << "Creating TensorRT engine from ONNX model using trtexec..." << std::endl;
        if (isMixedPrecision) {
            std::cout << "Mixed precision mode: using --stronglyTyped to preserve original layer precision from ONNX" << std::endl;
        }

        std::string onnxPathStr = ConvertWCharToChar(onnxPath);
        std::string enginePathStr = ConvertWCharToChar(enginePath);

        std::cout << "ONNX path: " << onnxPathStr << std::endl;
        std::cout << "Engine path: " << enginePathStr << std::endl;
        std::cout << "Pool size: " << poolSizeMB << " MB" << std::endl;

        // Check if ONNX file exists
        if (!std::filesystem::exists(onnxPathStr)) {
            std::cerr << "ONNX file not found: " << onnxPathStr << std::endl;
            return false;
        }

        // Check if this is a dynamic ONNX model that needs shape specification
        bool isDynamicModel = false;
        std::filesystem::path modelPath(onnxPathStr);
        std::string filename = modelPath.filename().string();
        std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);

        if (filename.find("rmbg2_0") != std::string::npos) {
            isDynamicModel = true;
            std::cout << "Detected dynamic ONNX model: " << filename << std::endl;
        }

        // Build trtexec command. ATTENTION: Inspyrenet does not work with TensorRt 10.11, even in fp32!
        std::vector<std::string> cmdArgs;
		cmdArgs.push_back("trtexec.exe");
        cmdArgs.push_back("--onnx=" + onnxPathStr);
        cmdArgs.push_back("--saveEngine=" + enginePathStr);
        //cmdArgs.push_back("--verbose");

        // Add default memory pool size if not overridden by additionalArgs
        bool hasMemPoolSize = false;
        for (const auto& arg : additionalArgs) {
            if (arg.find("--memPoolSize") != std::string::npos) {
                hasMemPoolSize = true;
                break;
            }
        }
        if (!hasMemPoolSize) {
            cmdArgs.push_back("--memPoolSize=workspace:" + std::to_string(poolSizeMB));
        }

        // Add model-specific additional arguments
        for (const auto& arg : additionalArgs) {
            // For mixed precision models, we keep --fp16 but add --stronglyTyped to preserve FP32 layers
            cmdArgs.push_back(arg);
        }

        // Handle mixed precision: use appropriate TensorRT flags
        if (isMixedPrecision) {
            // For mixed precision models, we use --stronglyTyped to preserve layer precision from ONNX
            // and avoid forcing all layers to FP16 when --fp16 is specified
            cmdArgs.push_back("--stronglyTyped");
            std::cout << "Added --stronglyTyped flag to preserve original layer precision from ONNX" << std::endl;
        }

        // Add shape specification for dynamic models
        if (isDynamicModel) {
            // For RMBG2_0: input: pixel_values float32[1,3,height,width] and output float32[1,1,height,width]
            // Set to 1024x1024 as the standard size for RMBG models
            cmdArgs.push_back("--shapes=pixel_values:1x3x1024x1024");
            std::cout << "Added shape specification for dynamic model: input:1x3x1024x1024" << std::endl;
        }

        // Convert command to string for display
        std::string cmdStr;
        for (const auto& arg : cmdArgs) {
            cmdStr += arg + " ";
        }
        std::cout << "Executing command: " << cmdStr << std::endl;

        // Execute trtexec
        bool success = ExecuteTrtexecCommand(cmdArgs);

        if (success) {
            // Verify engine file was created
            if (std::filesystem::exists(enginePathStr)) {
                auto fileSize = std::filesystem::file_size(enginePathStr);
                double sizeMB = static_cast<double>(fileSize) / (1024.0 * 1024.0);
                std::cout << L"✅ TensorRT engine created successfully: " << enginePathStr << std::endl;
                std::cout << "Engine size: " << std::fixed << std::setprecision(1) << sizeMB << " MB" << std::endl;
                return true;
            }
            else {
                std::cerr << L"❌ Engine file was not created" << std::endl;
                return false;
            }
        }
        else {
            std::cerr << L"❌ trtexec conversion failed" << std::endl;
            return false;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in CreateTensorRtEngine: " << e.what() << std::endl;
        return false;
    }
}

bool ImageMattingFactory::ExecuteTrtexecCommand(const std::vector<std::string>& cmdArgs, bool isTest, int timeoutMs) {
    // Build command line string
    std::string cmdLine;
    for (size_t i = 0; i < cmdArgs.size(); ++i) {
        if (i > 0) cmdLine += " ";
        // Quote arguments that contain spaces
        if (cmdArgs[i].find(' ') != std::string::npos) {
            cmdLine += "\"" + cmdArgs[i] + "\"";
        }
        else {
            cmdLine += cmdArgs[i];
        }
    }

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (!isTest) {
        si.dwFlags = STARTF_USESTDHANDLES;
        si.hStdOutput = GetStdHandle(STD_OUTPUT_HANDLE);
        si.hStdError = GetStdHandle(STD_ERROR_HANDLE);
    }

    std::cout << "Final command line: [" << cmdLine << "]" << std::endl;

    BOOL result = CreateProcessA(
        nullptr,                    // No module name (use command line)
        &cmdLine[0],               // Command line
        nullptr,                   // Process handle not inheritable
        nullptr,                   // Thread handle not inheritable
        TRUE,                      // Set handle inheritance to TRUE
        0,                         // No creation flags
        nullptr,                   // Use parent's environment block
        nullptr,                   // Use parent's starting directory
        &si,                       // Pointer to STARTUPINFO structure
        &pi                        // Pointer to PROCESS_INFORMATION structure
    );

    if (!result) {
        if (!isTest) {
            std::cerr << "Failed to create process. Error: " << GetLastError() << std::endl;
        }
        return false;
    }

    // Wait for process completion with timeout
    DWORD waitResult = WaitForSingleObject(pi.hProcess, timeoutMs > 0 ? timeoutMs : INFINITE);

    bool success = false;
    if (waitResult == WAIT_OBJECT_0) {
        DWORD exitCode;
        if (GetExitCodeProcess(pi.hProcess, &exitCode)) {
            success = (exitCode == 0);
        }
    }
    else if (waitResult == WAIT_TIMEOUT) {
        if (!isTest) {
            std::cerr << "Process timed out" << std::endl;
        }
        TerminateProcess(pi.hProcess, 1);
    }

    // Close process and thread handles
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);

    return success;
}

int ImageMattingFactory::PrepareModels(bool forceRecreate) {
    try {
        std::cout << "=== Creating TensorRT engines for all ONNX models ===" << std::endl;
        std::cout << "Force recreate: " << (forceRecreate ? "Yes" : "No") << std::endl;


        // Get executable directory for copying models
        wchar_t exePath[MAX_PATH];
        GetModuleFileNameW(NULL, exePath, MAX_PATH);
        std::filesystem::path exeDir = std::filesystem::path(exePath).parent_path();

        std::cout << "Executable directory: " << exeDir.string() << std::endl;

        // Define source model directories in library project
		std::filesystem::path libModelsMattingDir = exeDir.parent_path().parent_path() / "ImageMatterLib" / "ModelsMatting";
		std::filesystem::path libModelsHeadDetectorDir = exeDir.parent_path().parent_path() / "ImageMatterLib" / "ModelsVggHeadDetector";

        // Define target model directories in executable path
        std::filesystem::path exeModelsMattingDir = exeDir / "ModelsMatting";
        std::filesystem::path exeModelsHeadDetectorDir = exeDir / "ModelsVggHeadDetector";

        int enginesCreated = 0;
        int enginesSkipped = 0;
        int enginesFailed = 0;
        int filesCopied = 0;

        // Initialize model configs
        InitializeModelConfigs();

        // Process each model directory
        std::vector<std::pair<std::filesystem::path, std::filesystem::path>> modelDirPairs = {
            {libModelsMattingDir, exeModelsMattingDir},
            {libModelsHeadDetectorDir, exeModelsHeadDetectorDir}
        };

        for (const auto& dirPair : modelDirPairs) {
            const auto& sourceDir = dirPair.first;
            const auto& targetDir = dirPair.second;

            if (!std::filesystem::exists(sourceDir)) {
                std::cout << "Source directory does not exist: " << sourceDir.string() << std::endl;
                continue;
            }

            std::cout << "\n--- Processing directory: " << sourceDir.string() << " ---" << std::endl;

            // Create target directory if it doesn't exist
            if (!std::filesystem::exists(targetDir)) {
                std::cout << "Creating target directory: " << targetDir.string() << std::endl;
                std::filesystem::create_directories(targetDir);
            }

            // Scan for ONNX models in source directory
            std::vector<std::filesystem::path> onnxFiles;
            try {
                for (const auto& entry : std::filesystem::directory_iterator(sourceDir)) {
                    if (entry.is_regular_file()) {
                        std::wstring ext = GetFileExtension(entry.path().c_str());
                        std::transform(ext.begin(), ext.end(), ext.begin(), ::towlower);
                        if (ext == L".onnx") {
                            onnxFiles.push_back(entry.path());
                        }
                    }
                }
            }
            catch (const std::filesystem::filesystem_error& ex) {
                std::cerr << "Error scanning directory " << sourceDir.string() << ": " << ex.what() << std::endl;
                continue;
            }

            std::cout << "Found " << onnxFiles.size() << " ONNX models in " << sourceDir.string() << std::endl;

            #if false
            // Create TensorRT engines for each ONNX model
            for (const auto& onnxPath : onnxFiles) {
                std::cout << "\n--- Processing ONNX model ---" << std::endl;
                std::cout << "ONNX file: " << onnxPath.string() << std::endl;

                // Generate engine path in the same source directory
                std::filesystem::path enginePath = onnxPath;
                enginePath.replace_extension(".engine");

                std::cout << "Target engine: " << enginePath.string() << std::endl;

                // Check if engine already exists in source directory
                bool engineExists = std::filesystem::exists(enginePath);
                if (engineExists && !forceRecreate) {
                    std::cout << "Engine already exists in library directory, skipping creation" << std::endl;
                    enginesSkipped++;
                } else {
                    if (engineExists && forceRecreate) {
                        std::cout << "Engine exists but force recreate is enabled" << std::endl;
                    }

                    // Determine model type and get configuration
                    ModelType modelType = ModelType::RMBG1_4; // Default
                    std::vector<std::string> modelArgs;
                    int modelPoolSize = 2048;
                    bool isMixedPrecision = false;

                    // Try to determine model type from filename
                    std::string filename = onnxPath.filename().string();
                    std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);

                    if (filename.find("rmbg") != std::string::npos) {
                        if (filename.find("2.0") != std::string::npos) {
                            modelType = ModelType::RMBG2_0;
                        } else {
                            modelType = ModelType::RMBG1_4;
                        }
                    } else if (filename.find("inspyrenet") != std::string::npos) {
                        modelType = ModelType::INSPYRENET;
                    } else if (filename.find("vgg") != std::string::npos || filename.find("head") != std::string::npos) {
                        // This is likely a head detection model, skip TensorRT engine creation
                        // as head detection might use different processing
                        std::cout << "Skipping TensorRT engine creation for head detection model" << std::endl;
                        continue;
                    }

                    // Get model-specific configuration
                    auto configIt = s_modelConfigs.find(modelType);
                    if (configIt != s_modelConfigs.end()) {
                        modelArgs = configIt->second.trtexecArgs;
                        modelPoolSize = configIt->second.poolSizeMB;
                        isMixedPrecision = configIt->second.isMixedPrecision;

                        std::cout << "Using model-specific pool size: " << modelPoolSize << " MB" << std::endl;
                        if (isMixedPrecision) {
                            std::cout << "Model configured for mixed precision (preserving FP32 layers)" << std::endl;
                        }
                        if (!modelArgs.empty()) {
                            std::cout << "Using model-specific trtexec arguments: ";
                            for (const auto& arg : modelArgs) {
                                std::cout << arg << " ";
                            }
                            std::cout << std::endl;
                        }
                    }

                    // Create the engine in the library directory
                    std::cout << "Creating TensorRT engine in library directory (this may take several minutes)..." << std::endl;
                    auto startTime = std::chrono::high_resolution_clock::now();

                    bool success = CreateTensorRtEngine(onnxPath.c_str(), enginePath.c_str(), modelPoolSize, modelArgs, isMixedPrecision);

                    auto endTime = std::chrono::high_resolution_clock::now();
                    auto duration = std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);

                    if (success) {
                        std::cout << "SUCCESS: Engine created in " << duration.count() << " seconds" << std::endl;

                        // Get file size for reporting
                        try {
                            auto fileSize = std::filesystem::file_size(enginePath);
                            std::cout << "Engine size: " << (fileSize / (1024 * 1024)) << " MB" << std::endl;
                        }
                        catch (...) {
                            std::cout << "Engine size: Unable to determine" << std::endl;
                        }

                        enginesCreated++;
                    }
                    else {
                        std::cout << "FAILED: Engine creation failed after " << duration.count() << " seconds" << std::endl;
                        enginesFailed++;
                    }
                }
            }

            #endif
            // Now copy all model files (ONNX and engine) from source to target directory
            std::cout << "\n--- Copying model files from library to executable directory ---" << std::endl;
            try {
                for (const auto& entry : std::filesystem::directory_iterator(sourceDir)) {
                    if (entry.is_regular_file()) {
                        std::wstring ext = GetFileExtension(entry.path().c_str());
                        std::transform(ext.begin(), ext.end(), ext.begin(), ::towlower);

                        // Copy ONNX and engine files
                        if (ext == L".onnx" || ext == L".engine") {
                            std::filesystem::path sourceFile = entry.path();
                            std::filesystem::path targetFile = targetDir / sourceFile.filename();

                            bool shouldCopy = true;

                            // Check if target file exists and compare modification times
                            if (std::filesystem::exists(targetFile)) {
                                auto sourceTime = std::filesystem::last_write_time(sourceFile);
                                auto targetTime = std::filesystem::last_write_time(targetFile);

                                if (sourceTime <= targetTime) {
                                    std::cout << "Target file is up to date, skipping: " << targetFile.filename().string() << std::endl;
                                    shouldCopy = false;
                                }
                            }

                            if (shouldCopy) {
                                std::cout << "Copying: " << sourceFile.filename().string() << " -> " << targetFile.string() << std::endl;
                                std::filesystem::copy_file(sourceFile, targetFile, std::filesystem::copy_options::overwrite_existing);
                                filesCopied++;
                            }
                        }
                    }
                }
            }
            catch (const std::filesystem::filesystem_error& ex) {
                std::cerr << "Error copying files from " << sourceDir.string() << " to " << targetDir.string() << ": " << ex.what() << std::endl;
            }
        }

        std::cout << "\n=== Engine Creation and File Copy Summary ===" << std::endl;
        std::cout << "Engines created: " << enginesCreated << std::endl;
        std::cout << "Engines skipped (already exist): " << enginesSkipped << std::endl;
        std::cout << "Engine creation failures: " << enginesFailed << std::endl;
        std::cout << "Files copied to executable directory: " << filesCopied << std::endl;

        if (enginesFailed > 0) {
            std::cout << "WARNING: Some engines failed to create. Check the error messages above." << std::endl;
        }

        return enginesCreated;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in CreateAllTensorRtEngines: " << e.what() << std::endl;
        return 0;
    }
}

std::vector<std::pair<ModelInfo, bool>> ImageMattingFactory::GetModelEngineStatus() {
    std::vector<std::pair<ModelInfo, bool>> result;

    try {
        // Initialize model configs and scan models
        InitializeModelConfigs();
        ScanAvailableModels();

        for (const auto& model : s_availableModels) {
            std::wstring ext = GetFileExtension(model.path.c_str());
            std::transform(ext.begin(), ext.end(), ext.begin(), ::towlower);

            bool hasEngine = false;

            if (ext == L".onnx") {
                // Check if corresponding .engine file exists
                std::wstring enginePath = model.path;
                size_t dotPos = enginePath.find_last_of(L'.');
                if (dotPos != std::wstring::npos) {
                    enginePath = enginePath.substr(0, dotPos) + L".engine";
                }
                hasEngine = FileExists(enginePath.c_str());
            }
            else if (ext == L".engine" || ext == L".trt") {
                // This is already a TensorRT engine
                hasEngine = true;
            }

            result.emplace_back(model, hasEngine);
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in GetModelEngineStatus: " << e.what() << std::endl;
    }

    return result;
}

// HELPER FUNCTION IMPLEMENTATIONS

void ImageMattingFactory::InitializeModelConfigs() {
    static bool initialized = false;
    static std::mutex initMutex;

    std::lock_guard<std::mutex> lock(initMutex);
    if (initialized) return;

    {
        // InsPyReNet configuration - fp16 disabled due to precision issues
        s_modelConfigs[ModelType::INSPYRENET] = ModelTypeConfig(
            "InsPyReNet_(\\d+)x(\\d+)\\.(onnx|trt|engine)",
            NormalizationParams::ImageNet(),
            false,  // isRgba
            true,   // hasMultipleSizes
            ResizeMethod::EXTEND_SHRINK_LANCZOS,  // Use extend/shrink with Lanczos
            {},     // No additional trtexec args (fp16 disabled for this model)
            2048,   // poolSizeMB
            false,  // isMixedPrecision
            true    // useTensorRT
        );

        // IndexNet configuration
        s_modelConfigs[ModelType::INDEXNET] = ModelTypeConfig(
            "indexnet_matting_(\\d+)x(\\d+)\\.(onnx|trt|engine)",
            NormalizationParams::ImageNet(),
            true,   // isRgba
            true,   // hasMultipleSizes
            ResizeMethod::EXTEND_SHRINK_LANCZOS,  // Use extend/shrink with Lanczos
			{ "--fp16" },						 // Enable fp16 for IndexNet
			2048,								 // poolSizeMB
            false,                               // isMixedPrecision
            true                                 // useTensorRT
        );

        // RMBG1_4 configuration
        s_modelConfigs[ModelType::RMBG1_4] = ModelTypeConfig(
            "rmbg1_4_fp32.*\\.(onnx|trt|engine)",
            NormalizationParams::MinusHalfToPlusHalf(),
            false,  // isRgba
            false,  // hasMultipleSizes (fixed 1024x1024)
			ResizeMethod::EXTEND_SHRINK_LANCZOS,
            {},
            2048,   // poolSizeMB
            false,  // isMixedPrecision - will add --stronglyTyped to preserve original layer precision
            true    // useTensorRT
        );

        // RMBG2_0 configuration
        s_modelConfigs[ModelType::RMBG2_0] = ModelTypeConfig(
            "rmbg2_0_fp16.*\\.(onnx|trt|engine)",
            NormalizationParams::ImageNet(),
            false,  // isRgba
            false,  // hasMultipleSizes (fixed 1024x1024)
            ResizeMethod::EXTEND_SHRINK_LANCZOS,
            {},
            2048,   // poolSizeMB
            false,  // isMixedPrecision - will add --stronglyTyped to preserve original layer precision
            true   // useTensorRT
        );
        // Ben2 configuration - mixed precision model (has both FP16 and FP32 layers)
        s_modelConfigs[ModelType::BEN2] = ModelTypeConfig(
            "ben2_fp16.*\\.(onnx|trt|engine)",
            NormalizationParams::Standard(),
            false,  // isRgba
            false,  // hasMultipleSizes (fixed 1024x1024)
            ResizeMethod::EXTEND_SHRINK_LANCZOS,
			{  },
            2048,   // poolSizeMB
            false,  // isMixedPrecision - will add --stronglyTyped to preserve original layer precision
            true    // useTensorRT
        );
    }

    initialized = true;
}

void ImageMattingFactory::ScanAvailableModels() {
    std::lock_guard<std::mutex> lock(s_scanMutex);

    if (s_modelsScanned) {
        return; // Already scanned
    }

    std::cout << "Scanning available models..." << std::endl;

    // Get executable directory
    wchar_t exePath[MAX_PATH];
    GetModuleFileNameW(NULL, exePath, MAX_PATH);
    std::filesystem::path exeDir = std::filesystem::path(exePath).parent_path();

    s_availableModels.clear();

    std::filesystem::path modelsDir = exeDir / "ModelsMatting";

    if (!std::filesystem::exists(modelsDir)) {
        std::wcout << L"Warning: Models directory not found: " << modelsDir.wstring() << std::endl;
        return;
    }

    std::cout << "Scanning models ..." << std::endl;

    // Scan each model type
    for (const auto& configPair : s_modelConfigs) {
        ModelType modelType = configPair.first;
        const ModelTypeConfig& config = configPair.second;
        try {
            for (const auto& entry : std::filesystem::directory_iterator(modelsDir)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    std::smatch matches;

                    if (std::regex_search(filename, matches, config.filenamePattern)) {
                        if (config.hasMultipleSizes && matches.size() > 2) {
                            // Extract dimensions from filename
                            int width = std::stoi(matches[1].str());
                            int height = std::stoi(matches[2].str());

                            ModelInfo model(entry.path().wstring(), modelType, width, height);
                            s_availableModels.push_back(model);

                            std::cout << "Found model: " << filename
                                      << " (" << width << "x" << height << ")" << std::endl;
                        } else if (!config.hasMultipleSizes) {
                            // Fixed size models
                            int width, height;
                            width = height = 1024; // Both RMBG models use 1024x1024

                            ModelInfo model(entry.path().wstring(), modelType, width, height);
                            s_availableModels.push_back(model);

                            std::cout << "Found fixed-size model: " << filename
                                      << " (" << width << "x" << height << ")" << std::endl;
                        }
                    }
                }
            }
        }
        catch (const std::filesystem::filesystem_error& ex) {
            std::cerr << "Filesystem error scanning models directory : " << ex.what() << std::endl;
        }
    }

    std::cout << "Found " << s_availableModels.size() << " total models" << std::endl;
    s_modelsScanned = true;
}

ModelInfo ImageMattingFactory::FindBestModel(ModelType modelType, int imageWidth, int imageHeight, BestModelSelectionMethod method, InferenceBackend backend) {
    // Get model type configuration first to check useTensorRT preference
    auto configIt = s_modelConfigs.find(modelType);
    if (configIt == s_modelConfigs.end()) {
        std::cerr << "Unknown model type configuration in FindBestModel" << std::endl;
        return ModelInfo();
    }
    const ModelTypeConfig& config = configIt->second;

    // Filter models by type and backend compatibility
    std::vector<ModelInfo> typeModels;
    for (const auto& model : s_availableModels) {
        if (model.type != modelType) continue;
        std::wstring ext = GetFileExtension(model.path.c_str());
        std::transform(ext.begin(), ext.end(), ext.begin(), ::towlower);
        bool backendOk = false;
        switch (backend) {
            case InferenceBackend::ONNX_ONLY:
                backendOk = (ext == L".onnx");
                break;
            case InferenceBackend::TENSORRT_ONLY:
            case InferenceBackend::TENSORRT_WITH_FALLBACK:
                backendOk = (ext == L".engine" || ext == L".trt");
                break;
            case InferenceBackend::AUTO:
            default:
                if (config.useTensorRT) {
                    // Prefer TensorRT engines, but allow ONNX as fallback
                    backendOk = (ext == L".onnx" || ext == L".engine" || ext == L".trt");
                } else {
                    // Prefer ONNX only when useTensorRT is false
                    backendOk = (ext == L".onnx");
                }
                break;
        }
        if (backendOk) {
            typeModels.push_back(model);
        }
    }

    if (typeModels.empty()) {
        std::cerr << "No models found for type " << static_cast<int>(modelType) << std::endl;
        return ModelInfo(); // Return empty model
    }

    // Sort models to prioritize preferred backend in AUTO mode
    if (backend == InferenceBackend::AUTO) {
        std::sort(typeModels.begin(), typeModels.end(), [&config](const ModelInfo& a, const ModelInfo& b) {
            std::wstring extA = GetFileExtension(a.path.c_str());
            std::wstring extB = GetFileExtension(b.path.c_str());
            std::transform(extA.begin(), extA.end(), extA.begin(), ::towlower);
            std::transform(extB.begin(), extB.end(), extB.begin(), ::towlower);

            bool aIsTensorRT = (extA == L".engine" || extA == L".trt");
            bool bIsTensorRT = (extB == L".engine" || extB == L".trt");

            if (config.useTensorRT) {
                // Prefer TensorRT models when useTensorRT is true
                if (aIsTensorRT && !bIsTensorRT) return true;
                if (!aIsTensorRT && bIsTensorRT) return false;
            } else {
                // Prefer ONNX models when useTensorRT is false
                if (!aIsTensorRT && bIsTensorRT) return true;
                if (aIsTensorRT && !bIsTensorRT) return false;
            }
            return false; // Keep original order if same type
        });
    }

    // Selection logic based on method
    switch (method) {
        case BestModelSelectionMethod::FIXED_SIZE:
            // For fixed-size models, just return the first available
            return typeModels[0];
        case BestModelSelectionMethod::ASPECT_RATIO_CLOSEST_FIT: {
            // Select by aspect ratio similarity first, then by closest size (same as previous ASPECT_RATIO)
            float imageAspectRatio = static_cast<float>(imageWidth) / imageHeight;
            const float ASPECT_RATIO_TOLERANCE = 0.25f;
            std::vector<ModelInfo> matchingAspectRatioModels;
            for (const auto& model : typeModels) {
                if (std::abs(model.aspectRatio - imageAspectRatio) <= ASPECT_RATIO_TOLERANCE) {
                    matchingAspectRatioModels.push_back(model);
                }
            }
            if (!matchingAspectRatioModels.empty()) {
                std::sort(matchingAspectRatioModels.begin(), matchingAspectRatioModels.end(),
                    [imageWidth, imageHeight](const ModelInfo& a, const ModelInfo& b) {
                        float aScore = std::abs(static_cast<float>(a.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(a.height) / imageHeight - 1.0f);
                        float bScore = std::abs(static_cast<float>(b.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(b.height) / imageHeight - 1.0f);
                        return aScore < bScore;
                    });
                return matchingAspectRatioModels[0];
            }
            std::sort(typeModels.begin(), typeModels.end(),
                [imageAspectRatio](const ModelInfo& a, const ModelInfo& b) {
                    return std::abs(a.aspectRatio - imageAspectRatio) < std::abs(b.aspectRatio - imageAspectRatio);
                });
            return typeModels[0];
        }
        case BestModelSelectionMethod::ASPECT_RATIO_CLOSEST_BIGGER_SIZE: {
            // Select by aspect ratio similarity first, then by closest bigger size
            float imageAspectRatio = static_cast<float>(imageWidth) / imageHeight;
            const float ASPECT_RATIO_TOLERANCE = 0.25f;
            std::vector<ModelInfo> matchingAspectRatioModels;
            for (const auto& model : typeModels) {
                if (std::abs(model.aspectRatio - imageAspectRatio) <= ASPECT_RATIO_TOLERANCE) {
                    matchingAspectRatioModels.push_back(model);
                }
            }
            if (!matchingAspectRatioModels.empty()) {
                // Filter models that are >= image dimensions
                std::vector<ModelInfo> biggerModels;
                for (const auto& model : matchingAspectRatioModels) {
                    if (model.width >= imageWidth && model.height >= imageHeight) {
                        biggerModels.push_back(model);
                    }
                }
                if (!biggerModels.empty()) {
                    // Sort by closest size among bigger models
                    std::sort(biggerModels.begin(), biggerModels.end(),
                        [imageWidth, imageHeight](const ModelInfo& a, const ModelInfo& b) {
                            float aScore = std::abs(static_cast<float>(a.width) / imageWidth - 1.0f) +
                                          std::abs(static_cast<float>(a.height) / imageHeight - 1.0f);
                            float bScore = std::abs(static_cast<float>(b.width) / imageWidth - 1.0f) +
                                          std::abs(static_cast<float>(b.height) / imageHeight - 1.0f);
                            return aScore < bScore;
                        });
                    return biggerModels[0];
                }
                // If no bigger models with matching aspect ratio, fall back to closest fit
                std::sort(matchingAspectRatioModels.begin(), matchingAspectRatioModels.end(),
                    [imageWidth, imageHeight](const ModelInfo& a, const ModelInfo& b) {
                        float aScore = std::abs(static_cast<float>(a.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(a.height) / imageHeight - 1.0f);
                        float bScore = std::abs(static_cast<float>(b.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(b.height) / imageHeight - 1.0f);
                        return aScore < bScore;
                    });
                return matchingAspectRatioModels[0];
            }
            // No matching aspect ratio models, sort by aspect ratio similarity and prefer bigger models
            std::sort(typeModels.begin(), typeModels.end(),
                [imageAspectRatio, imageWidth, imageHeight](const ModelInfo& a, const ModelInfo& b) {
                    float aAspectDiff = std::abs(a.aspectRatio - imageAspectRatio);
                    float bAspectDiff = std::abs(b.aspectRatio - imageAspectRatio);
                    if (std::abs(aAspectDiff - bAspectDiff) < 0.01f) {
                        // Similar aspect ratios, prefer bigger models
                        bool aBigger = (a.width >= imageWidth && a.height >= imageHeight);
                        bool bBigger = (b.width >= imageWidth && b.height >= imageHeight);
                        if (aBigger && !bBigger) return true;
                        if (!aBigger && bBigger) return false;
                        // Both bigger or both smaller, choose closest
                        float aScore = std::abs(static_cast<float>(a.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(a.height) / imageHeight - 1.0f);
                        float bScore = std::abs(static_cast<float>(b.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(b.height) / imageHeight - 1.0f);
                        return aScore < bScore;
                    }
                    return aAspectDiff < bAspectDiff;
                });
            return typeModels[0];
        }
        case BestModelSelectionMethod::SMALLEST_FIT: {
            // IndexNet: Select smallest model that is >= target dimensions
            std::vector<ModelInfo> suitableModels;
            for (const auto& model : typeModels) {
                if (model.width >= imageWidth && model.height >= imageHeight) {
                    suitableModels.push_back(model);
                }
            }
            if (suitableModels.empty()) {
                std::cerr << "No model large enough for " << imageWidth << "x" << imageHeight << std::endl;
                return ModelInfo();
            }
            std::sort(suitableModels.begin(), suitableModels.end(),
                [](const ModelInfo& a, const ModelInfo& b) {
                    long long aArea = static_cast<long long>(a.width) * a.height;
                    long long bArea = static_cast<long long>(b.width) * b.height;
                    return aArea < bArea;
                });
            return suitableModels[0];
        }
        case BestModelSelectionMethod::SMALLEST_AREA_FIT: {
            // New: Find model with width >= imageWidth and height >= imageHeight, smallest area
            std::vector<ModelInfo> suitableModels;
            for (const auto& model : typeModels) {
                if (model.width >= imageWidth && model.height >= imageHeight) {
                    suitableModels.push_back(model);
                }
            }
            if (suitableModels.empty()) {
                std::cerr << "No model large enough for " << imageWidth << "x" << imageHeight << std::endl;
                return ModelInfo();
            }
            std::sort(suitableModels.begin(), suitableModels.end(),
                [](const ModelInfo& a, const ModelInfo& b) {
                    long long aArea = static_cast<long long>(a.width) * a.height;
                    long long bArea = static_cast<long long>(b.width) * b.height;
                    return aArea < bArea;
                });
            return suitableModels[0];
        }
        case BestModelSelectionMethod::DEFAULT:
        default:
            // Use per-model-type logic for backward compatibility
            if (!config.hasMultipleSizes) {
                return typeModels[0];
            }
            if (modelType == ModelType::INSPYRENET) {
                // Use aspect ratio closest fit logic (same as previous ASPECT_RATIO behavior)
                float imageAspectRatio = static_cast<float>(imageWidth) / imageHeight;
                const float ASPECT_RATIO_TOLERANCE = 0.25f;
                std::vector<ModelInfo> matchingAspectRatioModels;
                for (const auto& model : typeModels) {
                    if (std::abs(model.aspectRatio - imageAspectRatio) <= ASPECT_RATIO_TOLERANCE) {
                        matchingAspectRatioModels.push_back(model);
                    }
                }
                if (!matchingAspectRatioModels.empty()) {
                    std::sort(matchingAspectRatioModels.begin(), matchingAspectRatioModels.end(),
                        [imageWidth, imageHeight](const ModelInfo& a, const ModelInfo& b) {
                            float aScore = std::abs(static_cast<float>(a.width) / imageWidth - 1.0f) +
                                          std::abs(static_cast<float>(a.height) / imageHeight - 1.0f);
                            float bScore = std::abs(static_cast<float>(b.width) / imageWidth - 1.0f) +
                                          std::abs(static_cast<float>(b.height) / imageHeight - 1.0f);
                            return aScore < bScore;
                        });
                    return matchingAspectRatioModels[0];
                }
                std::sort(typeModels.begin(), typeModels.end(),
                    [imageAspectRatio](const ModelInfo& a, const ModelInfo& b) {
                        return std::abs(a.aspectRatio - imageAspectRatio) < std::abs(b.aspectRatio - imageAspectRatio);
                    });
                return typeModels[0];
            } else if (modelType == ModelType::INDEXNET) {
                // Use smallest fit logic
                std::vector<ModelInfo> suitableModels;
                for (const auto& model : typeModels) {
                    if (model.width >= imageWidth && model.height >= imageHeight) {
                        suitableModels.push_back(model);
                    }
                }
                if (suitableModels.empty()) {
                    std::cerr << "No IndexNet model large enough for " << imageWidth << "x" << imageHeight << std::endl;
                    return ModelInfo();
                }
                std::sort(suitableModels.begin(), suitableModels.end(),
                    [](const ModelInfo& a, const ModelInfo& b) {
                        long long aArea = static_cast<long long>(a.width) * a.height;
                        long long bArea = static_cast<long long>(b.width) * b.height;
                        return aArea < bArea;
                    });
                return suitableModels[0];
            }
            // Default: return first model
            return typeModels[0];
    }
}

std::unique_ptr<ImageMatting> ImageMattingFactory::CreateInstance(
    const ModelInfo& modelInfo,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    cudaStream_t stream,
    InferenceBackend backend) {

    // Get model type configuration
    auto configIt = s_modelConfigs.find(modelInfo.type);
    if (configIt == s_modelConfigs.end()) {
        std::cerr << "Unknown model type configuration for CreateInstance" << std::endl;
        return nullptr;
    }

    const ModelTypeConfig& config = configIt->second;

    std::cout << "Creating instance for model: " << ConvertWCharToChar(modelInfo.path.c_str()) << std::endl;
    std::cout << "Model dimensions: " << modelInfo.width << "x" << modelInfo.height << std::endl;
    std::cout << "Image dimensions: " << imageWidth << "x" << imageHeight << std::endl;

    // Determine which backend to use based on preference and availability
    std::wstring modelPath = modelInfo.path;
    std::wstring extension = GetFileExtension(modelPath.c_str());
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    // Handle backend preference
    switch (backend) {
        case InferenceBackend::ONNX_ONLY:
            std::cout << "Backend preference: ONNX only" << std::endl;
            if (extension != L".onnx") {
                std::cerr << "Error: ONNX_ONLY backend requested but model is not .onnx: " << ConvertWCharToChar(modelPath.c_str()) << std::endl;
                return nullptr;
            }
            break;

        case InferenceBackend::TENSORRT_ONLY:
            std::cout << "Backend preference: TensorRT only" << std::endl;
            if (extension == L".onnx") {
                // Need to create TensorRT engine
                std::wstring enginePath = modelPath;
                size_t dotPos = enginePath.find_last_of(L'.');
                if (dotPos != std::wstring::npos) {
                    enginePath = enginePath.substr(0, dotPos) + L".engine";
                }

                if (!FileExists(enginePath.c_str())) {
                    std::cout << "Creating TensorRT engine from ONNX with 2048 MB pool size..." << std::endl;
                    if (!CreateTensorRtEngine(modelPath.c_str(), enginePath.c_str(), 2048, config.trtexecArgs, config.isMixedPrecision)) {
                        std::cerr << "Failed to create TensorRT engine for TENSORRT_ONLY backend" << std::endl;
                        return nullptr;
                    }
                }
                modelPath = enginePath;
            }
            break;

        case InferenceBackend::TENSORRT_WITH_FALLBACK:
            std::cout << "Backend preference: TensorRT with ONNX fallback" << std::endl;
            if (extension == L".onnx") {
                std::wstring enginePath = modelPath;
                size_t dotPos = enginePath.find_last_of(L'.');
                if (dotPos != std::wstring::npos) {
                    enginePath = enginePath.substr(0, dotPos) + L".engine";
                }

                if (FileExists(enginePath.c_str())) {
                    std::cout << "Using existing TensorRT engine: " << ConvertWCharToChar(enginePath.c_str()) << std::endl;
                    modelPath = enginePath;
                } else {
                    std::cout << "TensorRT engine not found. Creating from ONNX with 2048 MB pool size..." << std::endl;
                    if (CreateTensorRtEngine(modelPath.c_str(), enginePath.c_str(), 2048, config.trtexecArgs, config.isMixedPrecision)) {
                        std::cout << "TensorRT engine created successfully: " << ConvertWCharToChar(enginePath.c_str()) << std::endl;
                        modelPath = enginePath;
                    } else {
                        std::cout << "Failed to create TensorRT engine, falling back to ONNX" << std::endl;
                    }
                }
            }
            break;

        case InferenceBackend::AUTO:
        default:
            std::cout << "Backend preference: Auto" << std::endl;
            if (extension == L".onnx") {
                if (config.useTensorRT) {
                    std::cout << "Model config prefers TensorRT - checking for existing engine" << std::endl;
                    std::wstring enginePath = modelPath;
                    size_t dotPos = enginePath.find_last_of(L'.');
                    if (dotPos != std::wstring::npos) {
                        enginePath = enginePath.substr(0, dotPos) + L".engine";
                    }

                    if (FileExists(enginePath.c_str())) {
                        std::cout << "Using existing TensorRT engine: " << ConvertWCharToChar(enginePath.c_str()) << std::endl;
                        modelPath = enginePath;
                    } else {
                        std::cout << "TensorRT engine not found, using ONNX (no automatic creation in AUTO mode)" << std::endl;
                    }
                } else {
                    std::cout << "Model config prefers ONNX - using ONNX directly" << std::endl;
                }
            }
            break;
    }

    // Create the actual instance based on file extension
    std::wstring finalExtension = GetFileExtension(modelPath.c_str());
    std::transform(finalExtension.begin(), finalExtension.end(), finalExtension.begin(), ::tolower);

    std::cout << "Initializing ImageMatting with file: " << ConvertWCharToChar(modelPath.c_str()) << std::endl;
    std::cout << "Detected extension: " << ConvertWCharToChar(finalExtension.c_str()) << std::endl;

    if (finalExtension == L".onnx") {
        std::cout << "Using ONNX Runtime implementation" << std::endl;
        return InitOnnx(modelPath.c_str(), modelInfo.width, modelInfo.height, imageWidth, imageHeight, normParams, config.isRgba, config.resizeMethod, stream);
    }
    else if (finalExtension == L".trt" || finalExtension == L".engine") {
        std::cout << "Using TensorRT implementation" << std::endl;
        return InitTensorRt(modelPath.c_str(), modelInfo.width, modelInfo.height, imageWidth, imageHeight, normParams, config.isRgba, config.resizeMethod, stream);
    }
    else {
        std::wcerr << L"Error: Unsupported model format: " << finalExtension << std::endl;
        std::wcerr << L"Supported formats: .onnx, .trt, .engine" << std::endl;
        return nullptr;
    }
}
