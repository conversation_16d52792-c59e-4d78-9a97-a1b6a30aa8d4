#include "VideoBackgroundRemover.h"
#include "FrameProcessor.h"
#include "DirectVideoReader.h"
#include "DirectVideoWriterAlphaCuda.h"
#include "ImageMattingFactory.h"
#include "Helpers.h"
#include "AsyncDirectVideoReader.h"
#include "FrameMetadataStorage.h"
#include "FrameMetadata.h"
#include "ProcessingStep.h"
#include "ProcessBodyRegionsStep.h"
#include <cuda.h>
#include <cuda_runtime.h>
#include <string>
#include <memory>
#include <atomic>
#include <functional>
#include <chrono>
#include <iostream>
#include <iomanip>
#include <vector>
#include <set>
#include <algorithm>
#include <cmath>

extern "C" {
#include <libavutil/log.h>
}

// Structure to hold compressed alpha data for each frame (for two-phase processing)
struct FrameAlphaData {
    int frameIndex;
    unsigned char* encodedAlpha;
    size_t encodedSize;
    double timestamp;
};

// Forward declarations
int ProcessMultiPass(
    const std::wstring& inputPath,
    const std::wstring& outputPath,
    EngineType engine,
    CUcontext context,
    cudaStream_t stream,
    ProgressCallback progressCb,
    void* userData
);

// Helper function to convert wstring to string
std::string WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), static_cast<int>(wstr.size()), nullptr, 0, nullptr, nullptr);
    if (size_needed <= 0) return std::string();

    std::string strTo(size_needed, '\0');
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), static_cast<int>(wstr.size()), &strTo[0], size_needed, nullptr, nullptr);
    return strTo;
}
// Multi-pass processing implementation
int ProcessMultiPass(
    const std::wstring& inputPath,
    const std::wstring& outputPath,
    EngineType engine,
    CUcontext context,
    cudaStream_t stream,
    ProgressCallback progressCb,
    void* userData
) {
    try {

        ImageMattingFactory::PrepareModels();

        auto startTime = std::chrono::high_resolution_clock::now();

        // Create asynchronous video reader
        std::string inputPathStr = WStringToString(inputPath);
        auto reader = AsyncDirectVideoReader::Create(inputPathStr, context, true, 3);
        if (!reader) {
            std::wcerr << L"Failed to create async video reader for: " << inputPath << std::endl;
            return 1;
        }

        int videoWidth = reader->GetWidth();
        int videoHeight = reader->GetHeight();
        int totalFrames = reader->GetFrameCount();

        std::cout << "Video dimensions: " << videoWidth << "x" << videoHeight << std::endl;
        std::cout << "Total frames: " << totalFrames << std::endl;

        // Create frame metadata storage
        std::wstring tempDbPath = L"frame_metadata_temp_db";
        auto metadataStorage = FrameMetadataStorage::Create(tempDbPath, true);
        if (!metadataStorage) {
            std::cerr << "Failed to create frame metadata storage" << std::endl;
            return 1;
        }

        // Create CUDA stream for processing
        cudaStream_t processingStream;
        CUDA_CHECK(cudaStreamCreate(&processingStream));

        // ===== PASS 1: Initial Alpha Generation =====
        std::cout << "\n=== Pass 1: Initial Alpha Generation ===" << std::endl;

        auto initialAlphaStep = ProcessingStepFactory::CreateStep(ProcessingStepFactory::StepType::FINAL_INITIAL_ALPHA);
        if (!initialAlphaStep || !initialAlphaStep->Initialize(videoWidth, videoHeight, context, processingStream)) {
            std::cerr << "Failed to initialize FindInitialAlphaStep" << std::endl;
            return 1;
        }

        // Process all frames for initial alpha
        int frameIndex = 0;
        while (true) {
            float* frameData = nullptr;
			size_t bufferSize;
            double timestamp = reader->ReadFrame(&frameData, &bufferSize);

            if (timestamp < 0.0) break; // End of video

            // Use dynamic allocation to avoid large stack allocation
            auto frameMetadata = std::make_unique<FrameMetadata>();
            frameMetadata->frameIndex = frameIndex;
            frameMetadata->timestamp = timestamp;

            if (!initialAlphaStep->Process(frameData, *frameMetadata)) {
                std::cerr << "Failed to process frame " << frameIndex << " in initial alpha step" << std::endl;
                break;
            }

            if (!metadataStorage->SetFrameMetadata(frameIndex, *frameMetadata)) {
                std::cerr << "Failed to store frame metadata for frame " << frameIndex << std::endl;
                break;
            }

            frameIndex++;

            // Progress callback for pass 1
            if (progressCb && !progressCb(frameIndex, totalFrames * 4, userData)) {
                std::cout << "Processing cancelled by user" << std::endl;
                return 1;
            }
        }

        // Clean up initial alpha step to free memory
        initialAlphaStep.reset();
        reader->Reset(); // Reset reader for next pass

        std::cout << "Pass 1 completed: " << frameIndex << " frames processed" << std::endl;

        // ===== PASS 2: Head Detection =====
        std::cout << "\n=== Pass 2: Head Detection ===" << std::endl;

        auto detectHeadsStep = ProcessingStepFactory::CreateStep(ProcessingStepFactory::StepType::DETECT_HEADS);
        if (!detectHeadsStep || !detectHeadsStep->Initialize(videoWidth, videoHeight, context, processingStream)) {
            std::cerr << "Failed to initialize DetectHeadsStep" << std::endl;
            return 1;
        }

        // Process all frames for head detection
        frameIndex = 0;
        reader->Reset();
        while (true) {
            float* frameData = nullptr;
			size_t bufferSize;
			double timestamp = reader->ReadFrame(&frameData, &bufferSize);

            if (timestamp < 0.0) break; // End of video

            FrameMetadata frameMetadata;
            if (!metadataStorage->GetFrameMetadata(frameIndex, frameMetadata)) {
                std::cerr << "Failed to get frame metadata for frame " << frameIndex << std::endl;
                break;
            }

            if (!detectHeadsStep->Process(frameData, frameMetadata)) {
                std::cerr << "Failed to process frame " << frameIndex << " in head detection step" << std::endl;
                break;
            }

            if (!metadataStorage->SetFrameMetadata(frameIndex, frameMetadata)) {
                std::cerr << "Failed to update frame metadata for frame " << frameIndex << std::endl;
                break;
            }

            frameIndex++;

            // Progress callback for pass 2
            if (progressCb && !progressCb(totalFrames + frameIndex, totalFrames * 4, userData)) {
                std::cout << "Processing cancelled by user" << std::endl;
                return 1;
            }
        }

        // Clean up head detection step
        detectHeadsStep.reset();
        reader->Reset();

        std::cout << "Pass 2 completed: " << frameIndex << " frames processed" << std::endl;

        // ===== HEAD SIZE ANALYSIS =====
        std::cout << "\n=== Analyzing Head Sizes ===" << std::endl;

        std::set<int> requiredHeadSizes;
        for (int i = 0; i < totalFrames; ++i) {
            FrameMetadata frameMetadata;
            if (metadataStorage->GetFrameMetadata(i, frameMetadata) && frameMetadata.hasHeadDetection) {
                const auto& headDetections = frameMetadata.GetHeadDetections();
                for (const auto& head : headDetections) {
                    int headSize = static_cast<int>(std::max(head.width, head.height));

                    // Map head size to InsPyReNet model sizes
                    int requiredModelSize = (headSize <= 320) ? 320 : (headSize <= 640) ? 640 : 1024;
                    requiredHeadSizes.insert(requiredModelSize);
                }
            }
        }

        std::cout << "Required head model sizes: ";
        for (int size : requiredHeadSizes) {
            std::cout << size << " ";
        }
        std::cout << std::endl;

        // ===== PASS 3+: Head Region Processing (one pass per head size) =====
        int passNumber = 3;
        for (int headModelSize : requiredHeadSizes) {
            std::cout << "\n=== Pass " << passNumber << ": Head Region Processing (size " << headModelSize << ") ===" << std::endl;

            auto headRegionsStep = ProcessingStepFactory::CreateHeadRegionsStep(headModelSize);
            if (!headRegionsStep || !headRegionsStep->Initialize(videoWidth, videoHeight, context, processingStream)) {
                std::cerr << "Failed to initialize ProcessHeadRegionsStep for size " << headModelSize << std::endl;
                return 1;
            }

            // Process all frames for this head size
            frameIndex = 0;
            reader->Reset();
            while (true) {
                float* frameData = nullptr;
				size_t bufferSize;
				double timestamp = reader->ReadFrame(&frameData, &bufferSize);

                if (timestamp < 0.0) break; // End of video

                FrameMetadata frameMetadata;
                if (!metadataStorage->GetFrameMetadata(frameIndex, frameMetadata)) {
                    std::cerr << "Failed to get frame metadata for frame " << frameIndex << std::endl;
                    break;
                }

                if (!headRegionsStep->Process(frameData, frameMetadata)) {
                    std::cerr << "Failed to process frame " << frameIndex << " in head regions step" << std::endl;
                    break;
                }

                if (!metadataStorage->SetFrameMetadata(frameIndex, frameMetadata)) {
                    std::cerr << "Failed to update frame metadata for frame " << frameIndex << std::endl;
                    break;
                }

                frameIndex++;

                // Progress callback for head processing passes
                if (progressCb && !progressCb((totalFrames * 2) + frameIndex, totalFrames * 4, userData)) {
                    std::cout << "Processing cancelled by user" << std::endl;
                    return 1;
                }
            }

            // Clean up head regions step
            headRegionsStep.reset();
            reader->Reset();

            std::cout << "Pass " << passNumber << " completed: " << frameIndex << " frames processed" << std::endl;
            passNumber++;
        }

        // ===== FINAL PASS: Body Region Processing with Video Output =====
        std::cout << "\n=== Final Pass: Body Region Processing with Video Output ===" << std::endl;

        auto bodyRegionsStep = ProcessingStepFactory::CreateStep(ProcessingStepFactory::StepType::PROCESS_BODY_REGIONS);
        if (!bodyRegionsStep || !bodyRegionsStep->Initialize(videoWidth, videoHeight, context, processingStream)) {
            std::cerr << "Failed to initialize ProcessBodyRegionsStep" << std::endl;
            return 1;
        }

        // Create video writer for final output
        DirectVideoWriterAlphaCuda::OutputConfig outputConfig;
        outputConfig.width = videoWidth;
        outputConfig.height = videoHeight;
        outputConfig.frameRate = reader->GetFrameRate();
        std::string outputPathStr = WStringToString(outputPath);
        outputConfig.outputPath = outputPathStr;

        auto writer = DirectVideoWriterAlphaCuda::Create(outputConfig, context);
        if (!writer) {
            std::wcerr << L"Failed to create video writer for: " << outputPath << std::endl;
            return 1;
        }

        // Allocate RGBA output buffer
        void* d_rgbaBuffer = nullptr;
        size_t rgbaBufferSize = videoWidth * videoHeight * 4 * sizeof(float);
        CUDA_CHECK(cudaMalloc(&d_rgbaBuffer, rgbaBufferSize));

        // Process all frames for body regions and write output
        frameIndex = 0;
        reader->Reset();
        while (true) {
            float* frameData = nullptr;
			size_t bufferSize;
			double timestamp = reader->ReadFrame(&frameData, &bufferSize);

            if (timestamp < 0.0) break; // End of video

            FrameMetadata frameMetadata;
            if (!metadataStorage->GetFrameMetadata(frameIndex, frameMetadata)) {
                std::cerr << "Failed to get frame metadata for frame " << frameIndex << std::endl;
                break;
            }

            if (!bodyRegionsStep->Process(frameData, frameMetadata)) {
                std::cerr << "Failed to process frame " << frameIndex << " in body regions step" << std::endl;
                break;
            }

            // Generate final RGBA output from processed alpha and original frame
            // Extract foreground using the formula: foreground = (original - (1-alpha) * background) / alpha
            // This combines the refined alpha with the original RGB data to create the final output
            ProcessBodyRegionsStep* bodyStep = dynamic_cast<ProcessBodyRegionsStep*>(bodyRegionsStep.get());
            if (!bodyStep || !bodyStep->GenerateFinalOutput(frameData, *frameMetadata, static_cast<float*>(d_rgbaBuffer))) {
                std::cerr << "Failed to generate final RGBA output for frame " << frameIndex << std::endl;
                break;
            }

            if (!writer->WriteFrame(d_rgbaBuffer, rgbaBufferSize)) {
                std::cerr << "Failed to write frame " << frameIndex << std::endl;
                break;
            }

            frameIndex++;

            // Progress callback for final pass
            if (progressCb && !progressCb((totalFrames * 3) + frameIndex, totalFrames * 4, userData)) {
                std::cout << "Processing cancelled by user" << std::endl;
                break;
            }
        }

        // Finalize video output
        writer->Finalize();

        // Clean up
        bodyRegionsStep.reset();
        if (d_rgbaBuffer) cudaFree(d_rgbaBuffer);
        if (processingStream) cudaStreamDestroy(processingStream);

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count() / 1000.0;

        std::cout << "Final pass completed: " << frameIndex << " frames processed" << std::endl;
        std::cout << "Multi-pass processing completed: " << frameIndex << " frames processed in "
                 << std::fixed << std::setprecision(2) << duration << " seconds" << std::endl;

        return 0; // Success
    }
    catch (const std::exception& e) {
        std::cerr << "Error in ProcessMultiPass: " << e.what() << std::endl;
        return 1;
    }
}

int Test();

// Main function: Returns 0 on success, nonzero on error/cancel
int VideoBackgroundRemover(
    const std::wstring& inputVideo,
    const std::wstring& outputVideo,
    EngineType engine,
    ProgressCallback progressCb,
    void* userData
) {
    try {

        Test();

		// This forces the CUDA runtime to initialize
		cudaFree(0);

		// Optional: check for errors
		cudaError_t err = cudaGetLastError();
		if (err != cudaSuccess) {
			printf("CUDA init failed: %s\n", cudaGetErrorString(err));
			return -1;
		}

        // Initialize CUDA context
        CUcontext cudaContext;
        CUresult result = cuCtxCreate(&cudaContext, 0, 0);
        if (result != CUDA_SUCCESS) {
            std::cerr << "Failed to create CUDA context" << std::endl;
            return 1;
        }

        // Set the context for this thread
        cuCtxSetCurrent(cudaContext);

        // Create CUDA stream for processing
        cudaStream_t stream;
        CUDA_CHECK(cudaStreamCreate(&stream));

        // Process the video using multi-pass architecture
        int processResult = ProcessMultiPass(inputVideo, outputVideo, engine, cudaContext, stream, progressCb, userData);

        // Cleanup
        CUDA_CHECK(cudaStreamDestroy(stream));
        cuCtxDestroy(cudaContext);

        return processResult;
    }
    catch (const std::exception& e) {
        std::cerr << "Error in VideoBackgroundRemover: " << e.what() << std::endl;
        return 1;
    }
}
