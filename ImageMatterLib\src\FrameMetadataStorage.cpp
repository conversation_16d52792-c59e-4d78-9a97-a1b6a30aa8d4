#include "FrameMetadataStorage.h"
#include <iostream>
#include <sstream>
#include <random>
#include <algorithm>
#include <Windows.h>
#include <rocksdb/db.h>
#include <rocksdb/options.h>
#include <rocksdb/slice.h>
#include <rocksdb/status.h>
#include <rocksdb/iterator.h>

// Constructor
FrameMetadataStorage::FrameMetadataStorage()
    : m_db(nullptr)
    , m_initialized(false) {
}

// Destructor
FrameMetadataStorage::~FrameMetadataStorage() {
    Close();
}

// Create factory method
std::unique_ptr<FrameMetadataStorage> FrameMetadataStorage::Create(
    const std::wstring& dbPath,
    bool createNew) {

    auto storage = std::unique_ptr<FrameMetadataStorage>(new FrameMetadataStorage());

    if (!storage->Initialize(dbPath, createNew)) {
        return nullptr;
    }

    return storage;
}

// Initialize the storage
bool FrameMetadataStorage::Initialize(const std::wstring& dbPath, bool createNew) {
    m_dbPath = dbPath;

    // Convert wide string to string for RocksDB
    std::string dbPathStr;
    int size = WideCharToMultiByte(CP_UTF8, 0, dbPath.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (size > 0) {
        dbPathStr.resize(size - 1);
        WideCharToMultiByte(CP_UTF8, 0, dbPath.c_str(), -1, &dbPathStr[0], size, nullptr, nullptr);
    } else {
        std::wcerr << L"Failed to convert database path to UTF-8" << std::endl;
        return false;
    }

    // Configure RocksDB options
    rocksdb::Options options;
    options.create_if_missing = createNew;
    options.error_if_exists = createNew;

    // Open the database
    rocksdb::DB* db;
    rocksdb::Status status = rocksdb::DB::Open(options, dbPathStr, &db);

    if (!status.ok()) {
        std::wcerr << L"Failed to open RocksDB database: " << dbPath << L", Error: "
                   << status.ToString().c_str() << std::endl;
        return false;
    }

    m_db.reset(db);
    m_initialized = true;
    return true;
}

// Close and cleanup
void FrameMetadataStorage::Close() {
    m_db.reset();
    m_initialized = false;
}

// Helper method to create frame key
std::string FrameMetadataStorage::CreateFrameKey(int frameIndex) const {
    return "frame_" + std::to_string(frameIndex);
}

// Get frame metadata by index
bool FrameMetadataStorage::GetFrameMetadata(int frameIndex, FrameMetadata& metadata) const {
    if (!m_initialized || frameIndex < 0) {
        return false;
    }

    std::string key = CreateFrameKey(frameIndex);
    std::string value;

    rocksdb::Status status = m_db->Get(rocksdb::ReadOptions(), key, &value);
    if (!status.ok()) {
        if (!status.IsNotFound()) {
            std::cerr << "Failed to get frame metadata for index " << frameIndex
                      << ": " << status.ToString() << std::endl;
        }
        return false;
    }

    // Deserialize the data
    return FrameMetadata::Deserialize(
        reinterpret_cast<const unsigned char*>(value.data()),
        value.size(),
        metadata);
}

// Set frame metadata by index
bool FrameMetadataStorage::SetFrameMetadata(int frameIndex, const FrameMetadata& metadata) {
    if (!m_initialized || frameIndex < 0) {
        return false;
    }

    // Create a copy and ensure frame index is correct
    FrameMetadata metadataCopy = metadata;
    metadataCopy.frameIndex = frameIndex;

    // Serialize the metadata
    std::vector<unsigned char> serializedData = metadataCopy.Serialize();

    std::string key = CreateFrameKey(frameIndex);
    rocksdb::Slice value(reinterpret_cast<const char*>(serializedData.data()), serializedData.size());

    rocksdb::Status status = m_db->Put(rocksdb::WriteOptions(), key, value);
    if (!status.ok()) {
        std::cerr << "Failed to set frame metadata for index " << frameIndex
                  << ": " << status.ToString() << std::endl;
        return false;
    }

    return true;
}

// Delete frame metadata by index
bool FrameMetadataStorage::DeleteFrameMetadata(int frameIndex) {
    if (!m_initialized || frameIndex < 0) {
        return false;
    }

    std::string key = CreateFrameKey(frameIndex);
    rocksdb::Status status = m_db->Delete(rocksdb::WriteOptions(), key);

    if (!status.ok() && !status.IsNotFound()) {
        std::cerr << "Failed to delete frame metadata for index " << frameIndex
                  << ": " << status.ToString() << std::endl;
        return false;
    }

    return true;
}

// Check if frame metadata exists for given index
bool FrameMetadataStorage::HasFrameMetadata(int frameIndex) const {
    if (!m_initialized || frameIndex < 0) {
        return false;
    }

    std::string key = CreateFrameKey(frameIndex);
    std::string value;

    rocksdb::Status status = m_db->Get(rocksdb::ReadOptions(), key, &value);
    return status.ok();
}

// Get all frame indices that have metadata stored
std::vector<int> FrameMetadataStorage::GetAllFrameIndices() const {
    std::vector<int> indices;

    if (!m_initialized) {
        return indices;
    }

    rocksdb::Iterator* it = m_db->NewIterator(rocksdb::ReadOptions());
    for (it->SeekToFirst(); it->Valid(); it->Next()) {
        std::string key = it->key().ToString();
        if (key.substr(0, 6) == "frame_") {
            try {
                int frameIndex = std::stoi(key.substr(6));
                indices.push_back(frameIndex);
            } catch (const std::exception&) {
                // Skip invalid keys
            }
        }
    }

    delete it;
    std::sort(indices.begin(), indices.end());
    return indices;
}

// Flush any pending writes to disk
bool FrameMetadataStorage::Flush() {
    if (!m_initialized) {
        return false;
    }

    // RocksDB handles flushing automatically, but we can force a flush
    rocksdb::Status status = m_db->Flush(rocksdb::FlushOptions());
    if (!status.ok()) {
        std::cerr << "Failed to flush database: " << status.ToString() << std::endl;
        return false;
    }

    return true;
}

// TemporaryFrameMetadataStorage implementation

TemporaryFrameMetadataStorage::TemporaryFrameMetadataStorage(const std::wstring& tempDir)
    : m_deleteOnDestroy(true) {

    // Generate temporary database path
    std::wstring baseTempDir = tempDir;
    if (baseTempDir.empty()) {
        wchar_t tempPath[MAX_PATH];
        if (GetTempPathW(MAX_PATH, tempPath) > 0) {
            baseTempDir = tempPath;
        } else {
            baseTempDir = L".\\"; // Fallback to current directory
        }
    }

    // Generate unique database directory name
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1000, 9999);

    std::wstringstream ss;
    ss << baseTempDir;
    if (baseTempDir.back() != L'\\' && baseTempDir.back() != L'/') {
        ss << L"\\";
    }
    ss << L"frame_metadata_db_" << GetCurrentProcessId() << L"_" << dis(gen);
    m_tempDbPath = ss.str();

    // Create the storage
    m_storage = FrameMetadataStorage::Create(m_tempDbPath, true);
    if (!m_storage) {
        std::wcerr << L"Failed to create temporary frame metadata storage: " << m_tempDbPath << std::endl;
    }
}

TemporaryFrameMetadataStorage::~TemporaryFrameMetadataStorage() {
    // Close storage first
    m_storage.reset();

    // Delete temporary database directory
    if (m_deleteOnDestroy && !m_tempDbPath.empty()) {
        // RocksDB creates a directory, so we need to remove it recursively
        // For now, we'll just try to remove the directory (it should be empty after closing)
        RemoveDirectoryW(m_tempDbPath.c_str());
    }
}
