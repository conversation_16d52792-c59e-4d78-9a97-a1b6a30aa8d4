#pragma once

#include "ProcessingStep.h"
#include "FrameMetadata.h"
#include "ImageMatting.h"
#include <memory>
#include <vector>

/**
 * Processing step for body region processing
 * Uses IndexNet to refine alpha matte in body regions with uncertain alpha values
 */
class ProcessBodyRegionsStep : public ProcessingStep {
public:
    ProcessBodyRegionsStep();
    virtual ~ProcessBodyRegionsStep();

    // ProcessingStep interface implementation
    bool Initialize(int width, int height, CUcontext cudaContext, cudaStream_t stream) override;
    bool Process(float* rgbBuffer, FrameMetadata& frameMetadata) override;
    const char* GetStepName() const override { return "ProcessBodyRegions"; }
    bool IsInitialized() const override { return m_initialized; }

private:
    // IndexNet model for body region refinement
    std::unique_ptr<ImageMatting> m_indexNetImageMatter;

    float* m_decompressedAlpha;
    float* m_trimapBuffer;
    float* m_originalAlphaTexture;

    // CUDA arrays for texture operations
    cudaArray_t m_alphaArray;

    // Processing parameters
    int m_width;
    int m_height;
    CUcontext m_cudaContext;
    cudaStream_t m_stream;
    bool m_initialized;

    int m_regionSizeBody;
    int m_indexNetModelInputSize;
    int m_maxUncertainRegionWidthForBody;
    int m_numVerticalRegionCountBody;

    // IndexNet instance for body region refinement
    std::unique_ptr<ImageMatting> m_indexNet;

    // Helper methods
    bool AllocateBuffers();
    void DeallocateBuffers();
    bool DecompressAlpha(const FrameMetadata& frameMetadata);
    void ProcessBodyRegionsImpl();

public:
    // Generate final RGBA output combining processed alpha with original frame
    bool GenerateFinalOutput(float* rgbBuffer, const FrameMetadata& frameMetadata, float* outputRgba);
};
