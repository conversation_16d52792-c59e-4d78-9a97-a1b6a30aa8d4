#pragma once

#include <cstdint>
#include <cstring>
#include <vector>
#include <memory>
#include "HeadDetector.h" // For Box struct

/**
 * Frame metadata structure for database persistence between processing passes
 * This structure contains all data that needs to persist between video processing steps
 * Uses variable-length data storage for efficient memory usage
 */
struct FrameMetadata {
    // Frame identification
    int frameIndex;
    double timestamp;

    // Processing status flags
    bool hasInitialAlpha;
    bool hasHeadDetection;
    bool hasBodyProcessing;
    bool hasHeadProcessing;

    // Encoded alpha matte data (from initial alpha step) - variable length
    std::vector<unsigned char> encodedAlpha;

    // Head detection results - variable length
    std::vector<Box> headDetections;

    // Additional processing metadata (can be extended as needed)
    struct {
        int numBodyRegionsProcessed;
        int numHeadRegionsProcessed;
        float averageConfidence;
        // Reserved space for future extensions
        float reserved[16];
    } processingStats;

    // Constructor
    FrameMetadata() {
        Reset();
    }

    // Reset all data to initial state
    void Reset() {
        frameIndex = -1;
        timestamp = -1.0;
        hasInitialAlpha = false;
        hasHeadDetection = false;
        hasBodyProcessing = false;
        hasHeadProcessing = false;
        processingStats = {};

        // Clear vectors
        encodedAlpha.clear();
        headDetections.clear();
    }

    // Validate the metadata structure
    bool IsValid() const {
        return frameIndex >= 0 &&
               timestamp >= 0.0;
    }

    // Set encoded alpha data
    bool SetEncodedAlpha(const unsigned char* data, size_t size) {
        if (!data || size == 0) {
            return false;
        }
        encodedAlpha.assign(data, data + size);
        hasInitialAlpha = true;
        return true;
    }

    // Set encoded alpha data from vector
    void SetEncodedAlpha(const std::vector<unsigned char>& data) {
        encodedAlpha = data;
        hasInitialAlpha = !data.empty();
    }

    // Get encoded alpha data
    const std::vector<unsigned char>& GetEncodedAlpha() const {
        return encodedAlpha;
    }

    // Get encoded alpha data size
    size_t GetEncodedAlphaSize() const {
        return encodedAlpha.size();
    }

    // Set head detection results
    void SetHeadDetections(const std::vector<Box>& detections) {
        headDetections = detections;
        hasHeadDetection = !detections.empty();
    }

    // Get head detection results
    const std::vector<Box>& GetHeadDetections() const {
        return headDetections;
    }

    // Get number of head detections
    size_t GetNumHeadDetections() const {
        return headDetections.size();
    }
    // Serialization methods for database storage
    std::vector<unsigned char> Serialize() const;
    bool Deserialize(const std::vector<unsigned char>& data);
    static bool Deserialize(const unsigned char* data, size_t size, FrameMetadata& metadata);
};
