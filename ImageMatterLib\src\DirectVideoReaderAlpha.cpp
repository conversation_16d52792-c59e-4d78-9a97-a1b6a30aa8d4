// DirectVideoReaderAlpha.cpp
#include "DirectVideoReaderAlpha.h"
#include "Helpers.h"

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/error.h>
#include <libavutil/imgutils.h>
#include <libavutil/pixdesc.h>
#include <libswscale/swscale.h>
}

#include <stdexcept>
#include <iostream>
#include <algorithm>
#include <cmath>

#pragma comment(lib, "avcodec.lib")
#pragma comment(lib, "avformat.lib")
#pragma comment(lib, "avutil.lib")
#pragma comment(lib, "swscale.lib")

// Helper function for FFmpeg error handling
static void CheckFFmpegError(int error) {
    if (error < 0) {
        char errBuf[AV_ERROR_MAX_STRING_SIZE] = { 0 };
        av_strerror(error, errBuf, AV_ERROR_MAX_STRING_SIZE);
        throw std::runtime_error(std::string("FFmpeg error: ") + errBuf);
    }
}

DirectVideoReaderAlpha::DirectVideoReaderAlpha() :
    m_formatContext(nullptr),
    m_codecContext(nullptr),
    m_frame(nullptr),
    m_rgbaFrame(nullptr),
    m_packet(nullptr),
    m_swsContext(nullptr),
    m_videoStreamIndex(-1),
    m_width(0),
    m_height(0),
    m_duration(0.0),
    m_frameRate({ 0, 1 }),
    m_timeBase({ 0, 1 }),
    m_hasAlpha(false),
    m_currentFrameTimestamp(0),
    m_isInitialized(false),
    m_isEof(false) {
}

DirectVideoReaderAlpha::~DirectVideoReaderAlpha() {
    Close();
}

std::shared_ptr<DirectVideoReaderAlpha> DirectVideoReaderAlpha::Create(const std::string& filePath) {
    std::shared_ptr<DirectVideoReaderAlpha> reader(new DirectVideoReaderAlpha());

    if (!reader->Initialize(filePath)) {
        return nullptr;
    }

    return reader;
}

Helpers::TextureFormat DirectVideoReaderAlpha::GetVideoStreamFormat() const {
    if (!m_isInitialized) return Helpers::TextureFormat::Unknown;
    return Helpers::TextureFormat::RGBA; // Always return RGBA for alpha videos
}

bool DirectVideoReaderAlpha::PixelFormatHasAlpha(int pixelFormat) const {
    const AVPixFmtDescriptor* desc = av_pix_fmt_desc_get(static_cast<AVPixelFormat>(pixelFormat));
    if (!desc) return false;

    // Check if the pixel format has an alpha component
    return (desc->flags & AV_PIX_FMT_FLAG_ALPHA) != 0;
}

bool DirectVideoReaderAlpha::Initialize(const std::string& filePath) {
    try {
        // Open input file
        CheckFFmpegError(avformat_open_input(&m_formatContext, filePath.c_str(), nullptr, nullptr));
        CheckFFmpegError(avformat_find_stream_info(m_formatContext, nullptr));

        // Find video stream
        m_videoStreamIndex = av_find_best_stream(m_formatContext, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
        if (m_videoStreamIndex < 0) {
            throw std::runtime_error("Could not find video stream in file: " + filePath);
        }

        // Get video properties
        AVStream* videoStream = m_formatContext->streams[m_videoStreamIndex];
        m_width = videoStream->codecpar->width;
        m_height = videoStream->codecpar->height;
        m_duration = static_cast<double>(m_formatContext->duration) / AV_TIME_BASE;
        m_frameRate = videoStream->avg_frame_rate;
        m_timeBase = videoStream->time_base;

        // Check if the video has alpha channel
        m_hasAlpha = PixelFormatHasAlpha(videoStream->codecpar->format);

        // Get pixel format name
        const char* pixFmtName = av_get_pix_fmt_name(static_cast<AVPixelFormat>(videoStream->codecpar->format));
        m_pixelFormatName = pixFmtName ? pixFmtName : "unknown";

        std::cout << "DirectVideoReaderAlpha: Video format: " << m_pixelFormatName << std::endl;
        std::cout << "DirectVideoReaderAlpha: Has alpha: " << (m_hasAlpha ? "Yes" : "No") << std::endl;

        // Find decoder
        const AVCodec* decoder = avcodec_find_decoder(videoStream->codecpar->codec_id);
        if (!decoder) {
            throw std::runtime_error("Could not find decoder for video stream");
        }

        std::cout << "DirectVideoReaderAlpha: Using decoder: " << decoder->name << std::endl;

        // Allocate codec context
        m_codecContext = avcodec_alloc_context3(decoder);
        if (!m_codecContext) {
            throw std::runtime_error("Could not allocate codec context");
        }

        // Copy codec parameters
        CheckFFmpegError(avcodec_parameters_to_context(m_codecContext, videoStream->codecpar));

        // Open codec (software decoding only)
        CheckFFmpegError(avcodec_open2(m_codecContext, decoder, nullptr));

        // Allocate frames and packet
        m_frame = av_frame_alloc();
        m_rgbaFrame = av_frame_alloc();
        m_packet = av_packet_alloc();

        if (!m_frame || !m_rgbaFrame || !m_packet) {
            throw std::runtime_error("Could not allocate frame or packet");
        }

        // Setup RGBA frame
        m_rgbaFrame->format = AV_PIX_FMT_RGBA;
        m_rgbaFrame->width = m_width;
        m_rgbaFrame->height = m_height;
        CheckFFmpegError(av_frame_get_buffer(m_rgbaFrame, 32));

        // Initialize software scaler for color space conversion
        m_swsContext = sws_getContext(
            m_width, m_height, m_codecContext->pix_fmt,
            m_width, m_height, AV_PIX_FMT_RGBA,
            SWS_BILINEAR, nullptr, nullptr, nullptr
        );

        if (!m_swsContext) {
            throw std::runtime_error("Could not initialize software scaler");
        }

        m_isInitialized = true;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error initializing DirectVideoReaderAlpha: " << e.what() << std::endl;
        Close();
        return false;
    }
}

bool DirectVideoReaderAlpha::DecodePacket() {
    while (true) {
        if (m_isEof) return false;

        int ret = av_read_frame(m_formatContext, m_packet);
        if (ret < 0) {
            if (ret == AVERROR_EOF) {
                m_isEof = true;
            }
            return false;
        }

        if (m_packet->stream_index != m_videoStreamIndex) {
            av_packet_unref(m_packet);
            continue;
        }

        ret = avcodec_send_packet(m_codecContext, m_packet);
        av_packet_unref(m_packet);

        if (ret < 0) {
            std::cerr << "Error sending packet to decoder" << std::endl;
            return false;
        }

        ret = avcodec_receive_frame(m_codecContext, m_frame);
        if (ret == AVERROR(EAGAIN)) {
            continue; // Need more packets
        } else if (ret < 0) {
            std::cerr << "Error receiving frame from decoder" << std::endl;
            return false;
        }

        // Successfully decoded a frame
        m_currentFrameTimestamp = m_frame->pts;
        return true;
    }
}

bool DirectVideoReaderAlpha::ConvertFrameToRgba(void* cpuRgbaBuffer, size_t bufferSize) {
    if (!m_frame || !cpuRgbaBuffer) return false;

    try {
        // Convert frame to RGBA using software scaler
        int ret = sws_scale(
            m_swsContext,
            m_frame->data, m_frame->linesize,
            0, m_height,
            m_rgbaFrame->data, m_rgbaFrame->linesize
        );

        if (ret < 0) {
            std::cerr << "Error converting frame to RGBA" << std::endl;
            return false;
        }

        // Copy RGBA data to CPU buffer
        size_t rgbaSize = m_width * m_height * 4; // RGBA bytes
        if (bufferSize < rgbaSize) {
            std::cerr << "Buffer size too small for RGBA data" << std::endl;
            return false;
        }

        // Direct memory copy from FFmpeg frame to CPU buffer
        memcpy(cpuRgbaBuffer, m_rgbaFrame->data[0], rgbaSize);
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error converting frame to RGBA: " << e.what() << std::endl;
        return false;
    }
}

double DirectVideoReaderAlpha::ConvertTimestampToSeconds(int64_t timestamp) const {
    if (timestamp == AV_NOPTS_VALUE) return -1.0;
    return static_cast<double>(timestamp) * m_timeBase.num / m_timeBase.den;
}

bool DirectVideoReaderAlpha::Seek(double timeInSeconds) {
    if (!m_isInitialized) return false;

    int64_t timestamp = static_cast<int64_t>(timeInSeconds * m_timeBase.den / m_timeBase.num);

    int ret = av_seek_frame(m_formatContext, m_videoStreamIndex, timestamp, AVSEEK_FLAG_BACKWARD);
    if (ret < 0) {
        std::cerr << "Error seeking to time: " << timeInSeconds << std::endl;
        return false;
    }

    // Flush codec buffers
    avcodec_flush_buffers(m_codecContext);
    m_isEof = false;

    return true;
}

// ReadFrame implementation - returns the frame timestamp
double DirectVideoReaderAlpha::ReadFrame(float* cpuRgbaBuffer, size_t bufferSize) {
    if (!m_isInitialized) return -1.0;

    // Decode a new frame
    if (!DecodePacket()) {
        return -1.0;
    }

    // Convert frame data to CPU RGBA memory
    if (!ConvertFrameToRgba(cpuRgbaBuffer, bufferSize)) {
        return -1.0;
    }

    // Return the frame timestamp in seconds
    return ConvertTimestampToSeconds(m_currentFrameTimestamp);
}

void DirectVideoReaderAlpha::Close() {
    if (m_swsContext) {
        sws_freeContext(m_swsContext);
        m_swsContext = nullptr;
    }
    if (m_packet) {
        av_packet_free(&m_packet);
    }
    if (m_rgbaFrame) {
        av_frame_free(&m_rgbaFrame);
    }
    if (m_frame) {
        av_frame_free(&m_frame);
    }
    if (m_codecContext) {
        avcodec_free_context(&m_codecContext);
    }
    if (m_formatContext) {
        avformat_close_input(&m_formatContext);
    }
    m_isInitialized = false;
    m_isEof = false;
}
