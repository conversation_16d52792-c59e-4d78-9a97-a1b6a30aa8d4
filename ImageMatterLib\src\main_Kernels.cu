#include "main_Kernels.cuh"
#include <stdio.h>

#define MIN_ALPHA_VALUE 5.0f / 255.0f
#define MAX_ALPHA_VALUE 251.0f / 255.0f

#ifdef __CUDACC__

// CUDA kernel to convert NV12 to planar float RGB
__global__ void ByteNv12ToPlanarFloatRgbKernel(unsigned char* nv12Data,
	float* rgbData,
	int width,
	int height,
	size_t nv12Pitch) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	int yIndex = y * nv12Pitch + x;
	int uvIndex = (y / 2) * nv12Pitch + (x & ~1);
	int uvOffset = height * nv12Pitch;

	// Get Y, U, V values
	float Y = (float)nv12Data[yIndex];
	float U = (float)nv12Data[uvOffset + uvIndex];
	float V = (float)nv12Data[uvOffset + uvIndex + 1];

	// Convert YUV to RGB
	Y = (Y - 16.0f) / 219.0f;
	U = (U - 128.0f) / 224.0f;
	V = (V - 128.0f) / 224.0f;

	float R = Y + 1.402f * V;
	float G = Y - 0.344f * U - 0.714f * V;
	float B = Y + 1.772f * U;

	// Clamp values
	R = fmaxf(0.0f, fminf(1.0f, R));
	G = fmaxf(0.0f, fminf(1.0f, G));
	B = fmaxf(0.0f, fminf(1.0f, B));

	// Calculate planar indices
	int pixelIndex = y * width + x;
	int planeSize = width * height;

	// Write to planar format (R plane first, then G, then B)
	rgbData[pixelIndex] = R;                     // R plane
	rgbData[pixelIndex + planeSize] = G;         // G plane
	rgbData[pixelIndex + 2 * planeSize] = B;     // B plane
}

// CUDA kernel to convert RGBA to NV12
__global__ void rgbaToNv12Kernel(float* rgbaData, unsigned char* nv12Data, int width, int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	int rgbaIndex = (y * width + x) * 4;
	float R = rgbaData[rgbaIndex + 0];
	float G = rgbaData[rgbaIndex + 1];
	float B = rgbaData[rgbaIndex + 2];

	// Convert RGB to YUV
	float Y = 0.299f * R + 0.587f * G + 0.114f * B;
	float U = -0.169f * R - 0.331f * G + 0.500f * B + 0.5f;
	float V = 0.500f * R - 0.419f * G - 0.081f * B + 0.5f;

	// Scale and clamp
	Y = Y * 219.0f + 16.0f;
	U = U * 224.0f + 128.0f;
	V = V * 224.0f + 128.0f;

	Y = fmaxf(0.0f, fminf(255.0f, Y));
	U = fmaxf(0.0f, fminf(255.0f, U));
	V = fmaxf(0.0f, fminf(255.0f, V));

	// Write Y plane
	int yIndex = y * width + x;
	nv12Data[yIndex] = (unsigned char)Y;

	// Write UV plane (only for even coordinates)
	if ((x % 2 == 0) && (y % 2 == 0)) {
		int uvIndex = (y / 2) * width + x;
		int uvOffset = width * height;
		nv12Data[uvOffset + uvIndex] = (unsigned char)U;
		nv12Data[uvOffset + uvIndex + 1] = (unsigned char)V;
	}
}

// Launcher function for NV12 to RGBA conversion
void launchByteNv12ToPlanarFloatRgb(unsigned char* nv12Data, float* rgbData, int width, int height, size_t nv12Pitch, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
	ByteNv12ToPlanarFloatRgbKernel << <gridSize, blockSize, 0, stream >> > (nv12Data, rgbData, width, height, nv12Pitch);
}

// Launcher function for RGBA to NV12 conversion
void launchRgbaToNv12(float* rgbaData, unsigned char* nv12Data, int width, int height, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
	rgbaToNv12Kernel << <gridSize, blockSize, 0, stream >> > (rgbaData, nv12Data, width, height);
}

// CUDA kernel to convert YUV420P to NV12
__global__ void yuv420pToNv12Kernel(unsigned char* yuv420pData, unsigned char* nv12Data,
	int width, int height,
	int srcYStride, int srcUStride, int srcVStride,
	int dstStride) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	// Copy Y plane (1:1)
	int yOffset = y * srcYStride + x;
	int dstYOffset = y * dstStride + x;
	nv12Data[dstYOffset] = yuv420pData[yOffset];

	// Process UV data (only on even rows and columns)
	if ((x % 2 == 0) && (y % 2 == 0)) {
		// Calculate source offsets - in YUV420P, U and V are separate planes
		int uOffset = width * height + (y / 2) * srcUStride + (x / 2);
		int vOffset = width * height + (height / 2) * srcVStride + (y / 2) * srcVStride + (x / 2);

		// Calculate destination offset - in NV12, UV is interleaved
		int dstUVOffset = height * dstStride + (y / 2) * dstStride + x;

		// Write interleaved UV data
		nv12Data[dstUVOffset] = yuv420pData[uOffset];       // U
		nv12Data[dstUVOffset + 1] = yuv420pData[vOffset];   // V
	}
}

// Launcher function for YUV420P to NV12 conversion
void launchYuv420pToNv12(unsigned char* yuv420pData, unsigned char* nv12Data,
	int width, int height,
	int srcYStride, int srcUStride, int srcVStride,
	int dstStride, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
	yuv420pToNv12Kernel << <gridSize, blockSize, 0, stream >> > (
		yuv420pData, nv12Data, width, height, srcYStride, srcUStride, srcVStride, dstStride);
}

// CUDA kernel for converting YUV420P (planar) to planar float RGB
// Each thread processes one pixel.
__global__ void Yuv420pToPlanarFloatRgbKernel(
	const unsigned char* y_plane_in,
	const unsigned char* u_plane_in,
	const unsigned char* v_plane_in,
	float* r_plane_out,
	float* g_plane_out,
	float* b_plane_out,
	int width,
	int height,
	int y_stride,
	int u_stride,
	int v_stride) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) {
		return;
	}

	// Get Y component
	unsigned char Y = y_plane_in[y * y_stride + x];

	// Get U and V components (subsampled by 2 in both dimensions)
	int uv_x = x / 2;
	int uv_y = y / 2;
	unsigned char U = u_plane_in[uv_y * u_stride + uv_x];
	unsigned char V = v_plane_in[uv_y * v_stride + uv_x];

	// Convert YUV to RGB (BT.601 standard, full range)
	// R = Y + 1.402 * (V - 128)
	// G = Y - 0.344136 * (U - 128) - 0.714136 * (V - 128)
	// B = Y + 1.772 * (U - 128)
	float fY = (float)Y;
	float fU = (float)U - 128.0f;
	float fV = (float)V - 128.0f;

	float R = fY + 1.402f * fV;
	float G = fY - 0.344136f * fU - 0.714136f * fV;
	float B = fY + 1.772f * fU;

	// Clamp to [0, 255] and normalize to [0, 1]
	r_plane_out[y * width + x] = fminf(fmaxf(R, 0.0f), 255.0f) / 255.0f;
	g_plane_out[y * width + x] = fminf(fmaxf(G, 0.0f), 255.0f) / 255.0f;
	b_plane_out[y * width + x] = fminf(fmaxf(B, 0.0f), 255.0f) / 255.0f;
}

// Launcher for Yuv420pToPlanarFloatRgbKernel
void launchYuv420pToPlanarFloatRgb(
	const unsigned char* y_plane,
	const unsigned char* u_plane,
	const unsigned char* v_plane,
	float* rgb_output,
	int width,
	int height,
	int y_stride,
	int u_stride,
	int v_stride,
	cudaStream_t stream) {
	// Calculate grid and block dimensions
	dim3 blockSize(16, 16); // Example block size
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);

	// Split the RGB output buffer into R, G, B planes
	float* r_plane_out = rgb_output;
	float* g_plane_out = rgb_output + width * height;
	float* b_plane_out = rgb_output + 2 * width * height;

	Yuv420pToPlanarFloatRgbKernel<<<gridSize, blockSize, 0, stream>>>(
		y_plane,
		u_plane,
		v_plane,
		r_plane_out,
		g_plane_out,
		b_plane_out,
		width,
		height,
		y_stride,
		u_stride,
		v_stride);
	// Error checking can be added here with cudaGetLastError() or cudaStreamSynchronize()
}


// CUDA kernel for converting YUV420P to NV12
// This kernel is provided for completeness if other parts of the system still require NV12.
__global__ void Yuv420pToNv12Kernel(
	const unsigned char* y_plane_in,
	const unsigned char* u_plane_in,
	const unsigned char* v_plane_in,
	unsigned char* nv12_buffer_out,
	int width,
	int height,
	int y_stride_in,
	int u_stride_in,
	int v_stride_in,
	int nv12_pitch_out) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x < width && y < height) {
		// Copy Y plane
		nv12_buffer_out[y * nv12_pitch_out + x] = y_plane_in[y * y_stride_in + x];
	}

	// Process UV plane (subsampled)
	if (x < width / 2 && y < height / 2) {
		int uv_offset_out = nv12_pitch_out * height + y * nv12_pitch_out + x * 2;
		nv12_buffer_out[uv_offset_out] = u_plane_in[y * u_stride_in + x];
		nv12_buffer_out[uv_offset_out + 1] = v_plane_in[y * v_stride_in + x];
	}
}

// Launcher for Yuv420pToNv12Kernel
void launchYuv420pToNv12(
	const unsigned char* yuv420p_buffer, // Assuming Y, U, V are packed sequentially in this buffer
	unsigned char* nv12_buffer,
	int width,
	int height,
	int y_stride_in,
	int u_stride_in,
	int v_stride_in,
	int nv12_pitch,
	cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);

	// Assuming yuv420p_buffer contains Y, U, V planes contiguously
	const unsigned char* y_plane_in = yuv420p_buffer;
	const unsigned char* u_plane_in = yuv420p_buffer + y_stride_in * height;
	const unsigned char* v_plane_in = yuv420p_buffer + y_stride_in * height + u_stride_in * (height / 2);

	Yuv420pToNv12Kernel<<<gridSize, blockSize, 0, stream>>>(
		y_plane_in,
		u_plane_in,
		v_plane_in,
		nv12_buffer,
		width,
		height,
		y_stride_in,
		u_stride_in,
		v_stride_in,
		nv12_pitch);
}

// CUDA kernel to convert alpha matte to NV12
__global__ void alphaMattingToNv12Kernel(float* alphaMatte, unsigned char* nv12Data, int width, int height, size_t nv12Pitch) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	// Calculate source and destination indices
	int alphaIndex = y * width + x;
	int yIndex = y * nv12Pitch + x;

	// Convert alpha value [0.0, 1.0] to Y value [0, 255]
	float alpha = alphaMatte[alphaIndex];
	unsigned char yValue = static_cast<unsigned char>(alpha * 255.0f);

	// Write to Y plane
	nv12Data[yIndex] = yValue;

	// Set U and V values to 128 (neutral gray) for even coordinates only
	// This ensures proper black/white rendering when displayed
	if ((x % 2 == 0) && (y % 2 == 0)) {
		int uvIndex = height * nv12Pitch + (y / 2) * nv12Pitch + x;
		nv12Data[uvIndex] = 128;     // U = 128 (neutral)
		nv12Data[uvIndex + 1] = 128; // V = 128 (neutral)
	}
}

// Launcher function for alpha matte to NV12 conversion
void launchAlphaMattingToNv12(float* alphaMatte, unsigned char* nv12Data, int width, int height, size_t nv12Pitch, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
	alphaMattingToNv12Kernel << <gridSize, blockSize, 0, stream >> > (alphaMatte, nv12Data, width, height, nv12Pitch);
}

// CUDA kernel to extract alpha from NV12 Y plane
__global__ void extractAlphaFromNv12Kernel(unsigned char* nv12Data, float* alphaData, int width, int height, size_t nv12Pitch) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	// Calculate indices
	int yIndex = y * nv12Pitch + x;
	int alphaIndex = y * width + x;

	// Extract Y value and convert to alpha [0.0, 1.0]
	unsigned char yValue = nv12Data[yIndex];
	float alpha = static_cast<float>(yValue) / 255.0f;

	// Write alpha value
	alphaData[alphaIndex] = alpha;
}

// Launcher function for extracting alpha from NV12 Y plane
void launchExtractAlphaFromNv12(unsigned char* nv12Data, float* alphaData, int width, int height, size_t nv12Pitch, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
	extractAlphaFromNv12Kernel << <gridSize, blockSize, 0, stream >> > (nv12Data, alphaData, width, height, nv12Pitch);
}

// CUDA kernel to convert RGBA to YUVA444 format for alpha video encoding
__global__ void rgbaToYuva444Kernel(float* rgbaData, unsigned char* yuvaData, int width, int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	int rgbaIndex = (y * width + x) * 4;
	float R = rgbaData[rgbaIndex + 0];
	float G = rgbaData[rgbaIndex + 1];
	float B = rgbaData[rgbaIndex + 2];
	float A = rgbaData[rgbaIndex + 3];

	// Convert RGB to YUV using BT.709 coefficients (HD standard)
	float Y = 0.2126f * R + 0.7152f * G + 0.0722f * B;
	float U = -0.1146f * R - 0.3854f * G + 0.5000f * B + 0.5f;
	float V = 0.5000f * R - 0.4542f * G - 0.0458f * B + 0.5f;

	// Scale to appropriate ranges for 10-bit encoding
	// Y: 64-940 for 10-bit (16-235 scaled to 10-bit)
	// UV: 64-960 for 10-bit (16-240 scaled to 10-bit)
	// A: 0-1023 for 10-bit alpha
	Y = Y * 876.0f + 64.0f;   // Scale to 10-bit Y range
	U = U * 896.0f + 64.0f;   // Scale to 10-bit U range
	V = V * 896.0f + 64.0f;   // Scale to 10-bit V range
	A = A * 1023.0f;          // Scale alpha to 10-bit range

	// Clamp values to valid ranges
	Y = fmaxf(64.0f, fminf(940.0f, Y));
	U = fmaxf(64.0f, fminf(960.0f, U));
	V = fmaxf(64.0f, fminf(960.0f, V));
	A = fmaxf(0.0f, fminf(1023.0f, A));

	// Calculate plane offsets for YUVA444 format
	int pixelIndex = y * width + x;
	int planeSize = width * height;

	// Write to separate planes (Y, U, V, A)
	// For 10-bit, we'll store as 16-bit values (2 bytes per sample)
	unsigned short* yuva16 = (unsigned short*)yuvaData;
	yuva16[pixelIndex] = (unsigned short)Y;                     // Y plane
	yuva16[pixelIndex + planeSize] = (unsigned short)U;         // U plane
	yuva16[pixelIndex + 2 * planeSize] = (unsigned short)V;     // V plane
	yuva16[pixelIndex + 3 * planeSize] = (unsigned short)A;     // A plane
}

// Launcher function for RGBA to YUVA444 conversion
void launchRgbaToYuva444(float* rgbaData, unsigned char* yuvaData, int width, int height, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
	rgbaToYuva444Kernel << <gridSize, blockSize, 0, stream >> > (rgbaData, yuvaData, width, height);
}

// CUDA kernel to combine planar RGB + Alpha into interleaved RGBA
__global__ void combineRgbAlphaToRgbaKernel(float* planarRgbData, float* alphaData, float* interleavedRgbaData, int width, int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	// Calculate indices
	int pixelIndex = y * width + x;
	int planeSize = width * height;
	int rgbaIndex = pixelIndex * 4;

	// Read from planar RGB format (R plane, G plane, B plane)
	float R = planarRgbData[pixelIndex];                    // R plane
	float G = planarRgbData[pixelIndex + planeSize];        // G plane
	float B = planarRgbData[pixelIndex + 2 * planeSize];    // B plane
	float A = alphaData[pixelIndex];                        // Alpha data

	// Write to interleaved RGBA format
	interleavedRgbaData[rgbaIndex + 0] = R;
	interleavedRgbaData[rgbaIndex + 1] = G;
	interleavedRgbaData[rgbaIndex + 2] = B;
	interleavedRgbaData[rgbaIndex + 3] = A;
}

// Launcher function for combining planar RGB + Alpha to interleaved RGBA
void launchCombineRgbAlphaToRgba(float* planarRgbData, float* alphaData, float* interleavedRgbaData, int width, int height, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
	combineRgbAlphaToRgbaKernel << <gridSize, blockSize, 0, stream >> > (planarRgbData, alphaData, interleavedRgbaData, width, height);
}

// CUDA kernel to generate synthetic alpha pattern (animated circular gradient)
__global__ void generateSyntheticAlphaKernel(float* alphaData, int width, int height, int frameNumber) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	// Calculate center of the image
	float centerX = width * 0.5f;
	float centerY = height * 0.5f;

	// Calculate distance from center
	float dx = x - centerX;
	float dy = y - centerY;
	float distance = sqrtf(dx * dx + dy * dy);

	// Create animated circular gradient
	float maxRadius = sqrtf(centerX * centerX + centerY * centerY);
	float normalizedDistance = distance / maxRadius;

	// Add animation based on frame number
	float animationPhase = (frameNumber % 120) / 120.0f; // 120 frame cycle
	float animatedRadius = 0.3f + 0.4f * sinf(animationPhase * 2.0f * 3.14159f);

	// Generate alpha value based on distance and animation
	float alpha;
	if (normalizedDistance < animatedRadius) {
		// Inside the circle - full opacity with smooth falloff
		alpha = 1.0f - (normalizedDistance / animatedRadius) * 0.3f;
	} else {
		// Outside the circle - fade to transparent
		float fadeDistance = (normalizedDistance - animatedRadius) / (1.0f - animatedRadius);
		alpha = fmaxf(0.0f, 0.7f * (1.0f - fadeDistance));
	}

	// Clamp alpha to valid range
	alpha = fmaxf(0.0f, fminf(1.0f, alpha));

	// Write alpha value
	int pixelIndex = y * width + x;
	alphaData[pixelIndex] = alpha;
}

// Launcher function for generating synthetic alpha pattern
void launchGenerateSyntheticAlpha(float* alphaData, int width, int height, int frameNumber, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
	generateSyntheticAlphaKernel << <gridSize, blockSize, 0, stream >> > (alphaData, width, height, frameNumber);
}

// CUDA kernel to convert float alpha (0.0-1.0) to uint8 alpha (0-255)
__global__ void floatToUint8AlphaKernel(float* floatAlpha, unsigned char* uint8Alpha, int width, int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	int pixelIndex = y * width + x;
	float alpha = floatAlpha[pixelIndex];

	// Clamp to [0.0, 1.0] and convert to [0, 255]
	alpha = fmaxf(0.0f, fminf(1.0f, alpha));
	uint8Alpha[pixelIndex] = static_cast<unsigned char>(alpha * 255.0f);
}

// Launcher function for float to uint8 alpha conversion
void launchFloatToUint8Alpha(float* floatAlpha, unsigned char* uint8Alpha, int width, int height, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
	floatToUint8AlphaKernel << <gridSize, blockSize, 0, stream >> > (floatAlpha, uint8Alpha, width, height);
}

// CUDA kernel to convert uint8 alpha (0-255) to float alpha (0.0-1.0)
__global__ void uint8ToFloatAlphaKernel(unsigned char* uint8Alpha, float* floatAlpha, int width, int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	int pixelIndex = y * width + x;
	unsigned char alpha = uint8Alpha[pixelIndex];

	// Convert from [0, 255] to [0.0, 1.0]
	floatAlpha[pixelIndex] = static_cast<float>(alpha) / 255.0f;
}

// Launcher function for uint8 to float alpha conversion
void launchUint8ToFloatAlpha(unsigned char* uint8Alpha, float* floatAlpha, int width, int height, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
	uint8ToFloatAlphaKernel << <gridSize, blockSize, 0, stream >> > (uint8Alpha, floatAlpha, width, height);
}

__global__ void DetectUncertainRegionsKernel(float* alphaBuffer, unsigned char* alphaRegionsBuffer, int width, int height, int regionSize) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int regionIdx = blockIdx.y;  // Since we're using 1D blocks, regionIdx is just blockIdx.y

	int numberOfRowsInRegion = (height + regionSize - 1) / regionSize;

	if (x >= width || regionIdx >= numberOfRowsInRegion) return;

	// Initialize flags for detection
	bool hasUncertainPixel = false;
	int uncertainPixelCount = 0;

	// Calculate region boundaries
	int baseY = regionIdx * regionSize;

	int startY = max(0, baseY);
	int endY = min(height, startY + regionSize);

	for (int y = startY; y < endY; y++) {
		float alphaValue = alphaBuffer[y * width + x];
		if (alphaValue > MIN_ALPHA_VALUE && alphaValue < MAX_ALPHA_VALUE) {
			hasUncertainPixel = true;
			uncertainPixelCount++;
		}
	}

	// Write result to output buffer
	int outputIdx = regionIdx * width + x;
	if (outputIdx < width * numberOfRowsInRegion) {
		alphaRegionsBuffer[outputIdx] = hasUncertainPixel ? 255 : 0;
	}
}

// Launcher function for alpha border detection
void launchDetectUncertainRegions(float* alphaBuffer, unsigned char* alphaRegionsBuffer, int width, int height, int regionSize, cudaStream_t stream) {
	// Use a 1D block for x dimension to ensure we process every x coordinate
	dim3 blockSize(256, 1);
	int numRegions = (height + regionSize - 1) / regionSize; // Ceiling division

	// Calculate grid size to cover all x coordinates and regions
	dim3 gridSize(
		(width + blockSize.x - 1) / blockSize.x,
		numRegions  // Each block handles one region
	);

	DetectUncertainRegionsKernel<<<gridSize, blockSize, 0, stream>>>(
		alphaBuffer, alphaRegionsBuffer, width, height, regionSize);

	// Check for kernel launch errors
	cudaError_t launchError = cudaGetLastError();
	if (launchError != cudaSuccess) {
		printf("CUDA kernel launch error: %s\n", cudaGetErrorString(launchError));
	}

	// Wait for kernel to complete and check results
	cudaError_t syncError = cudaStreamSynchronize(stream);
	if (syncError != cudaSuccess) {
		printf("CUDA stream synchronize error: %s\n", cudaGetErrorString(syncError));
	}
}

// CUDA kernel to generate basic trimap with specific value mapping and adjacency rules
__global__ void generateTrimapKernel(float* outputTrimap, const float* inputAlpha, int width, int height) {
	int tx = blockIdx.x * blockDim.x + threadIdx.x;
	int ty = blockIdx.y * blockDim.y + threadIdx.y;

	if (tx >= width || ty >= height)
		return;

	int idx = ty * width + tx;
	float alphaValue = inputAlpha[idx];

	// Generate basic trimap values
	float trimapValue;
	if (alphaValue <= MIN_ALPHA_VALUE) {
		trimapValue = 0.0f;  // Background
	}
	else if (alphaValue >= MAX_ALPHA_VALUE) {
		trimapValue = 1.0f;  // Foreground
	}
	else {
		trimapValue = 128.0f / 255.0f;  // Uncertain
	}

	// Check if there's an uncertain region (alpha <= MAX_ALPHA_VALUE) within 2 pixels
	bool hasUncertainNearby = false;

	// Search in a 2-pixel radius for uncertain regions
	for (int dy = -2; dy <= 2 && !hasUncertainNearby; dy++) {
		for (int dx = -2; dx <= 2 && !hasUncertainNearby; dx++) {
			if (dx * dx + dy * dy > 4) continue; // 2-pixel radius check

			int checkX = tx + dx;
			int checkY = ty + dy;

			// Check bounds
			if (checkX >= 0 && checkX < width && checkY >= 0 && checkY < height) {
				int checkIdx = checkY * width + checkX;
				float checkAlpha = inputAlpha[checkIdx];

				if (checkAlpha >= MIN_ALPHA_VALUE && checkAlpha <= MAX_ALPHA_VALUE) {
					hasUncertainNearby = true;
				}
			}
		}
	}

	// Extend uncertainty region: if there's uncertain nearby, make this pixel uncertain too
	if (hasUncertainNearby && trimapValue == 0.0f) {
		trimapValue = 128.0f / 255.0f;
	}

	outputTrimap[idx] = trimapValue;
}

// Launcher function for basic trimap generation
void launchGenerateTrimap(float* outputTrimap, const float* inputAlpha, int width, int height, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x,
		(height + blockSize.y - 1) / blockSize.y);

	generateTrimapKernel << <gridSize, blockSize, 0, stream >> > (
		outputTrimap, inputAlpha, width, height);
}

// CUDA kernel to generate head region trimap with directional scanning
__global__ void extendVerticallyHeadUncertaintyTrimapKernel(float* outputTrimap, const float* inputAlpha,
	int width, int height, int headX, int headY,
	int headWidth, int headHeight) {
	int tx = blockIdx.x * blockDim.x + threadIdx.x;

	if (tx >= headWidth)
		return;

	int globalX = headX + tx;

	if (globalX >= width)
		return;

	// Process vertical lines (one thread per column)
	// Top to bottom scan
	for (int y = headY; y < headY + headHeight; y++) {
		int idx = y * width + globalX;
		if (outputTrimap[idx] != 1.0f) {
			outputTrimap[idx] = 128.0f / 255.0f;
		}
		else break;
	}

	// Bottom to top scan
	for (int y = headY + headHeight - 1; y >= headY; y--) {
		int idx = y * width + globalX;
		if (outputTrimap[idx] != 1.0f) {
			outputTrimap[idx] = 128.0f / 255.0f;
		}
		else break;
	}
}
// CUDA kernel to generate head region trimap with directional scanning
__global__ void extendHorizontallyHeadUncertaintyTrimapKernel(float* outputTrimap, const float* inputAlpha,
	int width, int height, int headX, int headY,
	int headWidth, int headHeight) {
	int ty = blockIdx.y * blockDim.y + threadIdx.y;

	if (ty >= headHeight)
		return;

	int globalY = headY + ty;

	if (globalY >= height)
		return;

	// Left to right scan
	for (int x = headX; x < headX + headWidth; x++) {
		int idx = globalY * width + x;
		if (outputTrimap[idx] != 1.0f) {
			outputTrimap[idx] = 128.0f / 255.0f;
		}
		else break;
	}

	// Right to left scan
	for (int x = headX + headWidth - 1; x >= headX; x--) {
		int idx = globalY * width + x;
		if (outputTrimap[idx] != 1.0f) {
			outputTrimap[idx] = 128.0f / 255.0f;
		}
		else break;
	}
}

// Launcher function for head region trimap generation
void launchExtendHeadUncertaintyTrimap(float* outputTrimap, const float* inputAlpha,
	int width, int height, int headX, int headY,
	int headWidth, int headHeight, cudaStream_t stream) {
	dim3 blockSizeV(256, 1);
	dim3 gridSizeV((headWidth + blockSizeV.x - 1) / blockSizeV.x, 1);
	extendVerticallyHeadUncertaintyTrimapKernel <<<gridSizeV, blockSizeV, 0, stream>>> (
		outputTrimap, inputAlpha, width, height, headX, headY, headWidth, headHeight);
	dim3 blockSizeH(1, 256);
	dim3 gridSizeH(1, (headHeight + blockSizeH.y - 1) / blockSizeH.y);
	extendHorizontallyHeadUncertaintyTrimapKernel <<<gridSizeH, blockSizeH, 0, stream>>> (
		outputTrimap, inputAlpha, width, height, headX, headY, headWidth, headHeight);
}

// Optimized version with shared memory and reduced divergence
__global__ void updateAlphaBufferKernelRegion(
	float* __restrict__ outputAlphaBuffer,
	cudaTextureObject_t originalAlphaTexture,
	const float* __restrict__ trimapBuffer,
	const float* __restrict__ improvedAlpha,
	int x, int y, int width, int height, int modelSize, int padding) {

	const int tx = blockIdx.x * blockDim.x + threadIdx.x;
	const int ty = blockIdx.y * blockDim.y + threadIdx.y;
	const int effectiveSize = modelSize - 2 * padding;

	// Early bounds check - single condition
	if (tx >= effectiveSize || ty >= effectiveSize) return;

	const int dstX = x + tx;
	const int dstY = y + ty;

	// Combined bounds check
	if (dstX >= width || dstY >= height) return;

	const int dstIdx = dstY * width + dstX;

	#if false
	// Lines at top and bottom of region for debugging (keep the code but comment out!)
	if (ty == 0) {
		outputAlphaBuffer[dstIdx] = (tx % 2) == 0 ? 0.0f : 1.0f;
		return;
	}
	else if (ty == modelSize - 2 * padding - 1) {
		outputAlphaBuffer[dstIdx] = (tx % 2) == 0 ? 0.0f : 1.0f;
		return;
	}
	#endif

	// Early exit if not uncertain region - most important optimization
	const float trimapValue = trimapBuffer[dstIdx];
	if (trimapValue != 128.0f / 255.0f) return;

	// Optimized neighborhood search with reduced texture fetches
	constexpr int delta = 5;
	constexpr float minValue = 150.0f / 255.0f;
	constexpr int deltaSquared = delta * delta;

	// Use local variables to reduce global memory access
	const int startY = max(dstY - delta, 0);
	const int endY = min(dstY + delta + 1, height);
	const int startX = max(dstX - delta, 0);
	const int endX = min(dstX + delta + 1, width);

	// Optimized search with fewer texture fetches
	bool realAlphaZone = false;

#pragma unroll 3  // Partial unroll to balance register usage
	for (int checkY = startY; checkY < endY && !realAlphaZone; checkY++) {
		const int dy = checkY - dstY;
		const int dySquared = dy * dy;

#pragma unroll 3
		for (int checkX = startX; checkX < endX; checkX++) {
			const int dx = checkX - dstX;

			// Skip if outside circular region - but precompute dy*dy
			if (dx * dx + dySquared > deltaSquared) continue;

			// Texture fetch with optimal coordinate calculation
			const float alphaValue = tex2D<float>(originalAlphaTexture,
				checkX + 0.5f, checkY + 0.5f);

			if (alphaValue < minValue) {
				realAlphaZone = true;
				break;
			}
		}
	}

	// Conditional write - compiler will optimize this
	if (realAlphaZone) {
		const int srcIdx = (ty + padding) * modelSize + (tx + padding);
		outputAlphaBuffer[dstIdx] = improvedAlpha[srcIdx];
	}
}

// New launcher function for alpha buffer update
void launchUpdateAlphaBufferRegion(float* outputAlphaBuffer, cudaTextureObject_t originalAlphaTexture,
	const float* trimapBuffer, const float* improvedAlpha,
	int x, int y, int width, int height, int modelSize, int padding,
	cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((modelSize - 2 * padding + blockSize.x - 1) / blockSize.x,
		(modelSize - 2 * padding + blockSize.y - 1) / blockSize.y);

	updateAlphaBufferKernelRegion << <gridSize, blockSize, 0, stream >> > (
		outputAlphaBuffer, originalAlphaTexture, trimapBuffer, improvedAlpha,
		x, y, width, height, modelSize, padding);
}

// Optimized CUDA kernel using texture memory for original alpha buffer
__global__ void updateAlphaBufferKernelHead(float* outputAlphaBuffer,
	const float* trimapBuffer, const float* improvedAlpha,
	int x, int y, int width, int height, int alphaRegionSizeX, int alphaRegionSizeY) {
	int tx = blockIdx.x * blockDim.x + threadIdx.x;
	int ty = blockIdx.y * blockDim.y + threadIdx.y;

	// Only update the inner region (excluding padding)
	if (tx >= alphaRegionSizeX || ty >= alphaRegionSizeY)
		return;

	// Calculate destination coordinates in the full alpha buffer
	int dstX = x + tx;
	int dstY = y + ty;

	//printf("************ Updating: tx=%d, ty=%d, dstX=%d, dstY=%d\n", tx, ty, dstX, dstY);


	// Check if destination coordinates are within bounds
	if (dstX >= width || dstY >= height)
		return;

	// Calculate source and destination indices
	int srcIdx = ty * alphaRegionSizeX + tx;
	int dstIdx = dstY * width + dstX;

	// Early exit if not in uncertain trimap region
	if (trimapBuffer[dstIdx] != 128.0f / 255.0f)
		return;

	outputAlphaBuffer[dstIdx] = improvedAlpha[srcIdx];
}

// New launcher function for alpha buffer update
void launchUpdateAlphaBufferHead(float* outputAlphaBuffer,
	const float* trimapBuffer, const float* improvedAlpha,
	int x, int y, int width, int height, int alphaRegionSizeX, int alphaRegionSizeY,
	cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((alphaRegionSizeX  + blockSize.x - 1) / blockSize.x,
		(alphaRegionSizeY + blockSize.y - 1) / blockSize.y);

	updateAlphaBufferKernelHead << <gridSize, blockSize, 0, stream >> > (
		outputAlphaBuffer, trimapBuffer, improvedAlpha,
		x, y, width, height, alphaRegionSizeX, alphaRegionSizeY);
}


// Modify the existing prepareIndexNetInputKernel to use trimap instead of alpha
__global__ void prepareIndexNetInputKernel(float* outputBuffer, const float* inputRgbBuffer,
	const float* inputTrimap, int x, int y,
	int width, int height, int modelSizeX, int modelSizeY, int padding) {
	int tx = blockIdx.x * blockDim.x + threadIdx.x;
	int ty = blockIdx.y * blockDim.y + threadIdx.y;

	if (tx >= modelSizeX || ty >= modelSizeY)
		return;

	// Calculate source coordinates with padding offset
	int srcX = x - padding + tx;
	int srcY = y - padding + ty;

	// Check if source coordinates are within bounds
	if (srcX < 0 || srcX >= width || srcY < 0 || srcY >= height)
		return;

	// Calculate source and destination indices
	int srcIdx = (srcY * width + srcX);
	int dstIdx = ty * modelSizeX + tx;
	int dstChannelOffset = modelSizeX * modelSizeY;

	// Copy RGB channels (from planar format)
	int channelSize = width * height;
	outputBuffer[dstIdx] = inputRgbBuffer[srcIdx];                           // R from R plane
	outputBuffer[dstIdx + dstChannelOffset] = inputRgbBuffer[srcIdx + channelSize];     // G from G plane
	outputBuffer[dstIdx + 2 * dstChannelOffset] = inputRgbBuffer[srcIdx + 2 * channelSize]; // B from B plane
	outputBuffer[dstIdx + 3 * dstChannelOffset] = inputTrimap[srcIdx];  // A
}


// New launcher function for IndexNet input preparation
void launchPrepareIndexNetInput(float* outputBuffer, const float* inputRgbBuffer, const float* inputTrimap,
	int x, int y, int width, int height, int modelSizeX, int modelSizeY, int padding, cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((modelSizeX + blockSize.x - 1) / blockSize.x,
		(modelSizeY + blockSize.y - 1) / blockSize.y);

	prepareIndexNetInputKernel << <gridSize, blockSize, 0, stream >> > (
		outputBuffer, inputRgbBuffer, inputTrimap,
		x, y, width, height, modelSizeX, modelSizeY, padding);
}

// CUDA kernel to remove pixels under head boxes
__global__ void removePixelsUnderHeadsKernel(float* outputAlphaBuffer, const float* inputAlphaBuffer,
                                            const Box* headBoxes, int numHeads,
                                            int width, int height) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x >= width || y >= height)
        return;

    int pixelIdx = y * width + x;

    // Copy the input alpha value by default
    outputAlphaBuffer[pixelIdx] = inputAlphaBuffer[pixelIdx];

    // Check if this pixel is under any head box
    for (int i = 0; i < numHeads; i++) {
        const Box& head = headBoxes[i];
        if (x >= head.x && x < (head.x + head.width) &&
            y >= head.y && y < (head.y + head.height)) {
            // Pixel is under a head box, set it to 0
            outputAlphaBuffer[pixelIdx] = 0.0f;
            break;
        }
    }
}

// Launcher function for removing pixels under head boxes
void launchRemovePixelsUnderHeads(float* outputAlphaBuffer, const float* inputAlphaBuffer,
                                 const Box* headBoxes, int numHeads,
                                 int width, int height, cudaStream_t stream) {
    dim3 blockSize(16, 16);
    dim3 gridSize((width + blockSize.x - 1) / blockSize.x,
                  (height + blockSize.y - 1) / blockSize.y);

    removePixelsUnderHeadsKernel<<<gridSize, blockSize, 0, stream>>>(
        outputAlphaBuffer, inputAlphaBuffer, headBoxes, numHeads, width, height);
}

// CUDA kernel to extract head region without resizing
__global__ void extractHeadRegionKernel(
    const float* inputRgb, float* outputRgb,
    int extractX, int extractY, int extractWidth, int extractHeight,
    int inputWidth, int inputHeight) {

    int tx = blockIdx.x * blockDim.x + threadIdx.x;
    int ty = blockIdx.y * blockDim.y + threadIdx.y;
    int tc = blockIdx.z * blockDim.z + threadIdx.z;

    if (tx >= extractWidth || ty >= extractHeight || tc >= 3) return;

    int srcX = extractX + tx;
    int srcY = extractY + ty;
    if (srcX >= inputWidth || srcY >= inputHeight) return;

    int planeSize = inputWidth * inputHeight;
    int channelOffset = tc * planeSize;
    float value = inputRgb[channelOffset + srcY * inputWidth + srcX];

    int outputPlaneSize = extractWidth * extractHeight;
    int outputChannelOffset = tc * outputPlaneSize;
    outputRgb[outputChannelOffset + ty * extractWidth + tx] = value;
}

// Launcher function for head region extraction only (no resizing)
void launchExtractHeadRegion(
    const float* inputRgb, float* outputRgb,
    int extractX, int extractY, int extractWidth, int extractHeight,
    int inputWidth, int inputHeight,
    cudaStream_t stream) {
    dim3 blockSize(16, 16, 1);
    dim3 gridSize((extractWidth + blockSize.x - 1) / blockSize.x,
                  (extractHeight + blockSize.y - 1) / blockSize.y,
                  3); // RGB channels
    extractHeadRegionKernel<<<gridSize, blockSize, 0, stream>>>(
        inputRgb, outputRgb,
        extractX, extractY, extractWidth, extractHeight,
        inputWidth, inputHeight);
}

// CUDA kernel to update main alpha buffer from head region result
__global__ void updateAlphaFromHeadRegionKernel(
	float* mainAlphaBuffer,
	const float* headRegionAlpha,
	const float* trimap,
	int updateX, int updateY,
	int mainWidth, int mainHeight,
	int headRegionWidth, int headRegionHeight) {
	int tx = blockIdx.x * blockDim.x + threadIdx.x;
	int ty = blockIdx.y * blockDim.y + threadIdx.y;

	if (tx >= headRegionWidth || ty >= headRegionHeight) return;

	// Calculate main buffer coordinates (direct mapping)
	int mainX = updateX + tx;
	int mainY = updateY + ty;

	// Bounds check for main buffer
	if (mainX >= mainWidth || mainY >= mainHeight) return;

	// Direct access to head region data
	int headIdx = ty * headRegionWidth + tx;
	float headAlpha = headRegionAlpha[headIdx];
    int mainIdx = mainY * mainWidth + mainX;
#if true
  	// For inspyrenet, we always update. We must consider the trimap only if using IndexNet.
	mainAlphaBuffer[mainIdx] = headAlpha;
#else
	float trimapValue = trimap[mainIdx];

	// Only update if this is an uncertain region in the head trimap
	if (trimapValue == 128.0f / 255.0f) {
		mainAlphaBuffer[mainIdx] = headAlpha;
	}
#endif
}

// Launcher function for updating alpha from head region
void launchUpdateAlphaFromHeadRegion(
	float* mainAlphaBuffer,
	const float* headRegionAlpha,
	const float* trimap,
	int updateX, int updateY,
	int mainWidth, int mainHeight,
	int headRegionWidth, int headRegionHeight,
	cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((headRegionWidth + blockSize.x - 1) / blockSize.x,
		(headRegionHeight + blockSize.y - 1) / blockSize.y);

	updateAlphaFromHeadRegionKernel << <gridSize, blockSize, 0, stream >> > (
		mainAlphaBuffer, headRegionAlpha, trimap,
		updateX, updateY,
		mainWidth, mainHeight,
		headRegionWidth, headRegionHeight);
}

// CUDA kernel to rescan and tightly fit head region to non-background pixels (except neck direction)
__global__ void rescanHeadRegionKernel(
    const float* inputRgb, // Planar RGB input
    const float* alphaMatte, // Alpha matte (0=bg, 1=fg)
    Box* headBoxes, // Array of head boxes (in pixel coordinates)
    int numHeads,
    int width,
    int height,
	float bgThreshold
) {
    int headIdx = blockIdx.x * blockDim.x + threadIdx.x;
    if (headIdx >= numHeads) return;

    Box& head = headBoxes[headIdx];
    if (head.width <= 0 || head.height <= 0) return;

    int x0 = max(0, (int)head.x - 50);
	int y0 = max(0, (int)head.y - 50);
    int x1 = min(width, (int)(head.x + head.width) + 50);
    int y1 = min(height, (int)(head.y + head.height) + 50);

    // Only scan the region above the neck (e.g., top 80% of the head box)
    int minX = x1, minY = y1, maxX = x0, maxY = y0;
    bool found = false;
    for (int y = y0; y < y1; ++y) {
        for (int x = x0; x < x1; ++x) {
            int idx = y * width + x;
            float alpha = alphaMatte[idx];
            if (alpha > bgThreshold) {
                found = true;
                if (x < minX) minX = x;
                if (x > maxX) maxX = x;
                if (y < minY) minY = y;
                if (y > maxY) maxY = y;
            }
        }
    }
    if (found) {
        // Update head box to tightly fit non-background region (except neck direction)
        head.x = minX;
        head.y = minY;
        head.width = maxX - minX + 1;
        head.height = maxY - minY + 1;
    }
}

// Launcher for the rescanHeadRegionKernel
extern "C" void launchRescanHeadRegions(
    float* inputRgb,
    float* alphaMatte,
    Box* headBoxes,
    int numHeads,
    int width,
    int height,
    float bgThreshold,
    cudaStream_t stream
) {
    int blockSize = 32;
    int gridSize = (numHeads + blockSize - 1) / blockSize;
    rescanHeadRegionKernel<<<gridSize, blockSize, 0, stream>>>(
        inputRgb, alphaMatte, headBoxes, numHeads, width, height, bgThreshold);
}

// CUDA kernel to extract a region from a multi-channel buffer
__global__ void extractRegionKernel(const float* inputBuffer, float* outputBuffer,
                                   int inputWidth, int inputHeight,
                                   int regionX, int regionY, int regionWidth, int regionHeight,
                                   int channels) {
    int tx = blockIdx.x * blockDim.x + threadIdx.x;
    int ty = blockIdx.y * blockDim.y + threadIdx.y;
    int tc = blockIdx.z * blockDim.z + threadIdx.z;

    if (tx >= regionWidth || ty >= regionHeight || tc >= channels) return;

    int srcX = regionX + tx;
    int srcY = regionY + ty;
    if (srcX >= inputWidth || srcY >= inputHeight) return;

    int inputPlaneSize = inputWidth * inputHeight;
    int outputPlaneSize = regionWidth * regionHeight;

    int srcIdx = tc * inputPlaneSize + srcY * inputWidth + srcX;
    int dstIdx = tc * outputPlaneSize + ty * regionWidth + tx;

    outputBuffer[dstIdx] = inputBuffer[srcIdx];
}

// Launcher function for region extraction
void launchExtractRegion(const float* inputBuffer, float* outputBuffer, int inputWidth, int inputHeight,
                        int regionX, int regionY, int regionWidth, int regionHeight, int channels, cudaStream_t stream) {
    dim3 blockSize(16, 16, 1);
    dim3 gridSize((regionWidth + blockSize.x - 1) / blockSize.x,
                  (regionHeight + blockSize.y - 1) / blockSize.y,
                  channels);
    extractRegionKernel<<<gridSize, blockSize, 0, stream>>>(
        inputBuffer, outputBuffer, inputWidth, inputHeight,
        regionX, regionY, regionWidth, regionHeight, channels);
}

// CUDA kernel to blend alpha region back into main alpha buffer
__global__ void blendAlphaRegionKernel(float* mainAlpha, const float* regionAlpha,
                                      int mainWidth, int mainHeight,
                                      int regionX, int regionY, int regionWidth, int regionHeight) {
    int tx = blockIdx.x * blockDim.x + threadIdx.x;
    int ty = blockIdx.y * blockDim.y + threadIdx.y;

    if (tx >= regionWidth || ty >= regionHeight) return;

    int mainX = regionX + tx;
    int mainY = regionY + ty;
    if (mainX >= mainWidth || mainY >= mainHeight) return;

    int regionIdx = ty * regionWidth + tx;
    int mainIdx = mainY * mainWidth + mainX;

    // Simple replacement blend - could be enhanced with more sophisticated blending
    mainAlpha[mainIdx] = regionAlpha[regionIdx];
}

// Launcher function for alpha region blending
void launchBlendAlphaRegion(float* mainAlpha, const float* regionAlpha, int mainWidth, int mainHeight,
                           int regionX, int regionY, int regionWidth, int regionHeight, cudaStream_t stream) {
    dim3 blockSize(16, 16);
    dim3 gridSize((regionWidth + blockSize.x - 1) / blockSize.x,
                  (regionHeight + blockSize.y - 1) / blockSize.y);
    blendAlphaRegionKernel<<<gridSize, blockSize, 0, stream>>>(
        mainAlpha, regionAlpha, mainWidth, mainHeight,
        regionX, regionY, regionWidth, regionHeight);
}

// CUDA kernel for simple bilinear image resizing
__global__ void resizeImageKernel(const float* inputBuffer, float* outputBuffer,
                                 int inputWidth, int inputHeight,
                                 int outputWidth, int outputHeight, int channels) {
    int tx = blockIdx.x * blockDim.x + threadIdx.x;
    int ty = blockIdx.y * blockDim.y + threadIdx.y;
    int tc = blockIdx.z * blockDim.z + threadIdx.z;

    if (tx >= outputWidth || ty >= outputHeight || tc >= channels) return;

    // Calculate source coordinates with bilinear interpolation
    float srcX = (float)tx * (float)inputWidth / (float)outputWidth;
    float srcY = (float)ty * (float)inputHeight / (float)outputHeight;

    int x0 = (int)floorf(srcX);
    int y0 = (int)floorf(srcY);
    int x1 = min(x0 + 1, inputWidth - 1);
    int y1 = min(y0 + 1, inputHeight - 1);

    float fx = srcX - x0;
    float fy = srcY - y0;

    int inputPlaneSize = inputWidth * inputHeight;
    int outputPlaneSize = outputWidth * outputHeight;
    int channelOffset = tc * inputPlaneSize;

    // Bilinear interpolation
    float v00 = inputBuffer[channelOffset + y0 * inputWidth + x0];
    float v01 = inputBuffer[channelOffset + y0 * inputWidth + x1];
    float v10 = inputBuffer[channelOffset + y1 * inputWidth + x0];
    float v11 = inputBuffer[channelOffset + y1 * inputWidth + x1];

    float v0 = v00 * (1.0f - fx) + v01 * fx;
    float v1 = v10 * (1.0f - fx) + v11 * fx;
    float result = v0 * (1.0f - fy) + v1 * fy;

    int outputIdx = tc * outputPlaneSize + ty * outputWidth + tx;
    outputBuffer[outputIdx] = result;
}

// Launcher function for image resizing
void launchResizeImage(const float* inputBuffer, float* outputBuffer, int inputWidth, int inputHeight,
                      int outputWidth, int outputHeight, int channels, cudaStream_t stream) {
    dim3 blockSize(16, 16, 1);
    dim3 gridSize((outputWidth + blockSize.x - 1) / blockSize.x,
                  (outputHeight + blockSize.y - 1) / blockSize.y,
                  channels);
    resizeImageKernel<<<gridSize, blockSize, 0, stream>>>(
        inputBuffer, outputBuffer, inputWidth, inputHeight,
        outputWidth, outputHeight, channels);
}

// CUDA kernel for horizontal background estimation
__global__ void estimateBackgroundHorizontalKernel(const float* alphaBuffer, const float* rgbBuffer,
                                                   float* backgroundBuffer, int width, int height) {
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    int c = blockIdx.z * blockDim.z + threadIdx.z;

    if (y >= height || c >= 3) return;

    int planeSize = width * height;
    int channelOffset = c * planeSize;

    // For each row, estimate background by linear interpolation between known background pixels
    for (int x = 0; x < width; x++) {
        int idx = y * width + x;
        float alpha = alphaBuffer[idx];

        if (alpha <= 0.01f) {
            // This is a background pixel, copy directly
            backgroundBuffer[channelOffset + idx] = rgbBuffer[channelOffset + idx];
        } else {
            // Find nearest background pixels on left and right
            int leftBg = -1, rightBg = -1;

            // Search left
            for (int xl = x - 1; xl >= 0; xl--) {
                int leftIdx = y * width + xl;
                if (alphaBuffer[leftIdx] <= 0.01f) {
                    leftBg = xl;
                    break;
                }
            }

            // Search right
            for (int xr = x + 1; xr < width; xr++) {
                int rightIdx = y * width + xr;
                if (alphaBuffer[rightIdx] <= 0.01f) {
                    rightBg = xr;
                    break;
                }
            }

            // Interpolate or extrapolate
            if (leftBg >= 0 && rightBg >= 0) {
                // Interpolate between left and right
                float leftVal = rgbBuffer[channelOffset + y * width + leftBg];
                float rightVal = rgbBuffer[channelOffset + y * width + rightBg];
                float t = (float)(x - leftBg) / (float)(rightBg - leftBg);
                backgroundBuffer[channelOffset + idx] = leftVal * (1.0f - t) + rightVal * t;
            } else if (leftBg >= 0) {
                // Use left value
                backgroundBuffer[channelOffset + idx] = rgbBuffer[channelOffset + y * width + leftBg];
            } else if (rightBg >= 0) {
                // Use right value
                backgroundBuffer[channelOffset + idx] = rgbBuffer[channelOffset + y * width + rightBg];
            } else {
                // No background found, use a neutral gray
                backgroundBuffer[channelOffset + idx] = 0.5f;
            }
        }
    }
}

// Launcher function for horizontal background estimation
void launchEstimateBackgroundHorizontal(const float* alphaBuffer, const float* rgbBuffer, float* backgroundBuffer,
                                       int width, int height, cudaStream_t stream) {
    dim3 blockSize(1, 16, 1);
    dim3 gridSize(1, (height + blockSize.y - 1) / blockSize.y, 3);
    estimateBackgroundHorizontalKernel<<<gridSize, blockSize, 0, stream>>>(
        alphaBuffer, rgbBuffer, backgroundBuffer, width, height);
}

// CUDA kernel for vertical background estimation
__global__ void estimateBackgroundVerticalKernel(const float* alphaBuffer, const float* rgbBuffer,
                                                 float* backgroundBuffer, int width, int height) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int c = blockIdx.z * blockDim.z + threadIdx.z;

    if (x >= width || c >= 3) return;

    int planeSize = width * height;
    int channelOffset = c * planeSize;

    // For each column, estimate background by linear interpolation between known background pixels
    for (int y = 0; y < height; y++) {
        int idx = y * width + x;
        float alpha = alphaBuffer[idx];

        if (alpha <= 0.01f) {
            // This is a background pixel, copy directly
            backgroundBuffer[channelOffset + idx] = rgbBuffer[channelOffset + idx];
        } else {
            // Find nearest background pixels above and below
            int topBg = -1, bottomBg = -1;

            // Search up
            for (int yt = y - 1; yt >= 0; yt--) {
                int topIdx = yt * width + x;
                if (alphaBuffer[topIdx] <= 0.01f) {
                    topBg = yt;
                    break;
                }
            }

            // Search down
            for (int yb = y + 1; yb < height; yb++) {
                int bottomIdx = yb * width + x;
                if (alphaBuffer[bottomIdx] <= 0.01f) {
                    bottomBg = yb;
                    break;
                }
            }

            // Interpolate or extrapolate
            if (topBg >= 0 && bottomBg >= 0) {
                // Interpolate between top and bottom
                float topVal = rgbBuffer[channelOffset + topBg * width + x];
                float bottomVal = rgbBuffer[channelOffset + bottomBg * width + x];
                float t = (float)(y - topBg) / (float)(bottomBg - topBg);
                backgroundBuffer[channelOffset + idx] = topVal * (1.0f - t) + bottomVal * t;
            } else if (topBg >= 0) {
                // Use top value
                backgroundBuffer[channelOffset + idx] = rgbBuffer[channelOffset + topBg * width + x];
            } else if (bottomBg >= 0) {
                // Use bottom value
                backgroundBuffer[channelOffset + idx] = rgbBuffer[channelOffset + bottomBg * width + x];
            } else {
                // No background found, use a neutral gray
                backgroundBuffer[channelOffset + idx] = 0.5f;
            }
        }
    }
}

// Launcher function for vertical background estimation
void launchEstimateBackgroundVertical(const float* alphaBuffer, const float* rgbBuffer, float* backgroundBuffer,
                                     int width, int height, cudaStream_t stream) {
    dim3 blockSize(16, 1, 1);
    dim3 gridSize((width + blockSize.x - 1) / blockSize.x, 1, 3);
    estimateBackgroundVerticalKernel<<<gridSize, blockSize, 0, stream>>>(
        alphaBuffer, rgbBuffer, backgroundBuffer, width, height);
}

// CUDA kernel for foreground extraction using the formula: foreground = (original - (1-alpha) * background) / alpha
__global__ void extractForegroundKernel(const float* alphaBuffer, const float* rgbBuffer,
                                       const float* horizontalBg, const float* verticalBg,
                                       float* outputRgba, int width, int height) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x >= width || y >= height) return;

    int idx = y * width + x;
    int planeSize = width * height;
    float alpha = alphaBuffer[idx];

    // Combine horizontal and vertical background estimates (simple average for now)
    float bgR = (horizontalBg[idx] + verticalBg[idx]) * 0.5f;
    float bgG = (horizontalBg[idx + planeSize] + verticalBg[idx + planeSize]) * 0.5f;
    float bgB = (horizontalBg[idx + 2 * planeSize] + verticalBg[idx + 2 * planeSize]) * 0.5f;

    // Get original RGB values
    float origR = rgbBuffer[idx];
    float origG = rgbBuffer[idx + planeSize];
    float origB = rgbBuffer[idx + 2 * planeSize];

    // Extract foreground using the formula: foreground = (original - (1-alpha) * background) / alpha
    float fgR, fgG, fgB;
    if (alpha > 0.01f) {
        fgR = (origR - (1.0f - alpha) * bgR) / alpha;
        fgG = (origG - (1.0f - alpha) * bgG) / alpha;
        fgB = (origB - (1.0f - alpha) * bgB) / alpha;

        // Clamp to valid range
        fgR = fmaxf(0.0f, fminf(1.0f, fgR));
        fgG = fmaxf(0.0f, fminf(1.0f, fgG));
        fgB = fmaxf(0.0f, fminf(1.0f, fgB));
    } else {
        // For background pixels, use original color
        fgR = origR;
        fgG = origG;
        fgB = origB;
    }

    // Write to interleaved RGBA output
    int rgbaIdx = idx * 4;
    outputRgba[rgbaIdx + 0] = fgR;
    outputRgba[rgbaIdx + 1] = fgG;
    outputRgba[rgbaIdx + 2] = fgB;
    outputRgba[rgbaIdx + 3] = alpha;
}

// Launcher function for foreground extraction
void launchExtractForeground(const float* alphaBuffer, const float* rgbBuffer, const float* horizontalBg,
                           const float* verticalBg, float* outputRgba, int width, int height, cudaStream_t stream) {
    dim3 blockSize(16, 16);
    dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
    extractForegroundKernel<<<gridSize, blockSize, 0, stream>>>(
        alphaBuffer, rgbBuffer, horizontalBg, verticalBg, outputRgba, width, height);
}

#endif
