#include "FindInitialAlphaStep.h"
#include "ImageMattingFactory.h"
#include "Matting_Kernels.cuh"
#include "main_Kernels.cuh"
#include "Helpers.h"
#include <iostream>
#include <cuda_runtime.h>

FindInitialAlphaStep::FindInitialAlphaStep()
    : m_width(0)
    , m_height(0)
    , m_cudaContext(nullptr)
    , m_stream(nullptr)
    , m_initialized(false)
    , m_compressionBuffer(nullptr)
    , m_compressionBufferSize(0) {
}

FindInitialAlphaStep::~FindInitialAlphaStep() {
    DeallocateBuffers();
}

bool FindInitialAlphaStep::Initialize(int width, int height, CUcontext cudaContext, cudaStream_t stream) {
    if (m_initialized) {
        std::cerr << "FindInitialAlphaStep already initialized" << std::endl;
        return false;
    }

    m_width = width;
    m_height = height;
    m_cudaContext = cudaContext;
    m_stream = stream;

    // Set CUDA context
    CUresult contextResult = cuCtxSetCurrent(m_cudaContext);
    if (contextResult != CUDA_SUCCESS) {
        std::cerr << "Failed to set CUDA context in FindInitialAlphaStep: " << contextResult << std::endl;
        return false;
    }

    // Initialize initial image matting model using InsPyReNet
    m_initialImageMatter = ImageMattingFactory::Init(
        ModelType::INSPYRENET,
        m_width,
        m_height,
        m_stream,
        InferenceBackend::AUTO,
        ImageMattingFactory::BestModelSelectionMethod::ASPECT_RATIO_CLOSEST_FIT,
        false
    );

    if (!m_initialImageMatter) {
        std::cerr << "Failed to initialize InsPyReNet model for initial alpha matting" << std::endl;
        return false;
    }

    // Allocate processing buffers
    if (!AllocateBuffers()) {
        std::cerr << "Failed to allocate buffers for FindInitialAlphaStep" << std::endl;
        return false;
    }

    m_initialized = true;
    std::cout << "FindInitialAlphaStep initialized successfully (" << width << "x" << height << ")" << std::endl;
    return true;
}

bool FindInitialAlphaStep::Process(float* rgbBuffer, FrameMetadata& frameMetadata) {
    if (!m_initialized) {
        std::cerr << "FindInitialAlphaStep not initialized" << std::endl;
        return false;
    }

    // Set CUDA context for this thread
    CUresult contextResult = cuCtxSetCurrent(m_cudaContext);
    if (contextResult != CUDA_SUCCESS) {
        std::cerr << "Failed to set CUDA context in FindInitialAlphaStep::Process: " << contextResult << std::endl;
        return false;
    }

    try {
		CUDA_CHECK(cudaMemcpy(m_initialImageMatter->GetInputBuffer(), rgbBuffer, m_width * m_height * 3 * sizeof(float), cudaMemcpyDeviceToDevice));

        CUDA_CHECK(cudaStreamSynchronize(m_stream));

        // Run initial alpha matting inference
        if (!m_initialImageMatter->Infer()) {
            std::cerr << "Failed to run initial alpha matting inference" << std::endl;
            return false;
        }

        // Encode the alpha matte for storage
        unsigned char* encodedAlpha = nullptr;
        size_t encodedSize = 0;

        if (!EncodeAlpha(m_initialImageMatter->GetOutputBuffer(), &encodedAlpha, &encodedSize)) {
            std::cerr << "Failed to encode alpha matte" << std::endl;
            return false;
        }

        // Store encoded alpha in frame metadata
        if (!frameMetadata.SetEncodedAlpha(encodedAlpha, encodedSize)) {
            std::cerr << "Failed to store encoded alpha in frame metadata" << std::endl;
            if (encodedAlpha) free(encodedAlpha);
            return false;
        }

        // Clean up temporary encoded data
        if (encodedAlpha) {
            free(encodedAlpha);
        }

        // Mark this step as completed
        frameMetadata.hasInitialAlpha = true;

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in FindInitialAlphaStep::Process: " << e.what() << std::endl;
        return false;
    }
}

bool FindInitialAlphaStep::AllocateBuffers() {
    try {
        // Allocate RGB conversion buffer (not needed as we use model's input buffer directly)
        // m_rgbBufferSize = m_width * m_height * 3 * sizeof(float);
        // CUDA_CHECK(cudaMalloc(&m_rgbBuffer, m_rgbBufferSize));

        // Allocate compression buffer for alpha encoding
        m_compressionBufferSize = m_width * m_height * sizeof(float); // Worst case: no compression
        CUDA_CHECK(cudaMalloc(&m_compressionBuffer, m_compressionBufferSize));

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Failed to allocate buffers: " << e.what() << std::endl;
        DeallocateBuffers();
        return false;
    }
}

void FindInitialAlphaStep::DeallocateBuffers() {
    if (m_compressionBuffer) {
        cudaFree(m_compressionBuffer);
        m_compressionBuffer = nullptr;
    }

    m_compressionBufferSize = 0;
}

bool FindInitialAlphaStep::EncodeAlpha(float* alphaBuffer, unsigned char** encodedData, size_t* encodedSize) {
    if (!alphaBuffer || !encodedData || !encodedSize) {
        return false;
    }

    // For now, implement simple compression by converting float to byte and using basic RLE
    // In a production system, you might want to use a proper compression library

    size_t alphaPixelCount = m_width * m_height;

    // Allocate host buffer for alpha data
    float* hostAlphaBuffer = new float[alphaPixelCount];

    // Copy alpha data from GPU to host
    cudaError_t result = cudaMemcpy(hostAlphaBuffer, alphaBuffer,
                                   alphaPixelCount * sizeof(float),
                                   cudaMemcpyDeviceToHost);
    if (result != cudaSuccess) {
        std::cerr << "Failed to copy alpha data to host: " << cudaGetErrorString(result) << std::endl;
        delete[] hostAlphaBuffer;
        return false;
    }

    // Convert to bytes and apply simple compression
    unsigned char* byteAlpha = new unsigned char[alphaPixelCount];
    for (size_t i = 0; i < alphaPixelCount; ++i) {
        // Clamp and convert float [0,1] to byte [0,255]
        float val = std::max(0.0f, std::min(1.0f, hostAlphaBuffer[i]));
        byteAlpha[i] = static_cast<unsigned char>(val * 255.0f);
    }

    // For simplicity, just store the byte data directly (no compression for now)
    // In production, you could implement PNG compression or similar
    *encodedSize = alphaPixelCount;
    *encodedData = static_cast<unsigned char*>(malloc(*encodedSize));
    if (!*encodedData) {
        delete[] hostAlphaBuffer;
        delete[] byteAlpha;
        return false;
    }

    memcpy(*encodedData, byteAlpha, *encodedSize);

    // Clean up temporary buffers
    delete[] hostAlphaBuffer;
    delete[] byteAlpha;

    return true;
}
