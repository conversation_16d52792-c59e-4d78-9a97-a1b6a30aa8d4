# ImageMatter - CUDA ProRes 4444 AI Alpha Matting

## Build Commands
- **Build Debug**: `msbuild ImageMatter.sln /p:Configuration=Debug /p:Platform=x64`
- **Build Release**: `msbuild ImageMatter.sln /p:Configuration=Release /p:Platform=x64`
- **Build Library**: `msbuild ImageMatterLib\ImageMatterLib.vcxproj /p:Configuration=Debug /p:Platform=x64`
- **Clean**: `msbuild ImageMatter.sln /t:Clean`
- **Format Code**: Follow `.clang-format` settings (LLVM-based, tabs, 4-space indent)

## Architecture
- **Main App**: Console application for video background removal with AI alpha matting
- **ImageMatterLib**: DLL library for integration into other applications  
- **Two Processing Modes**: One-phase (faster) vs Two-phase (memory efficient)
- **AI Engines**: ONNX Runtime, TensorRT, or AUTO selection
- **Core Components**: FrameProcessor, ImageMatting (ONNX/TensorRT), HeadDetector, VideoBackgroundRemoval
- **CUDA Kernels**: Background estimation, alpha matting, ProRes encoding, head detection, resizing

## Dependencies
- **CUDA 12.9** with compute capability support
- **FFmpeg** for video I/O (H.264, ProRes 4444)
- **ONNX Runtime** for AI inference
- **TensorRT** for optimized inference
- **Models**: VGG head detector (`.onnx`), matting models in `ModelsMatting/`

## Code Style
- **Standard**: C++17, Windows-specific (`std::wstring` paths)
- **Formatting**: Tabs (4-width), LLVM-based, custom braces, no column limit
- **Headers**: `.h` for interfaces, `.cuh` for CUDA kernels
- **Naming**: PascalCase classes, camelCase functions, `d_` prefix for device memory
- **Error Handling**: Return codes (0=success), CUDA error checking required
